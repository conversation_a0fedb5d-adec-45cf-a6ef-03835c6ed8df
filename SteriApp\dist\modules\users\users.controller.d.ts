import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UserRole } from "../../common/decorators/roles.decorator";
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<{
        createdAt: Date;
        lastLogin: Date;
        userId: string;
        username: string;
        email: string;
        role: UserRole;
        language?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
