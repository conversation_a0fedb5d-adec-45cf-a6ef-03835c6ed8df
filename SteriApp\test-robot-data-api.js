#!/usr/bin/env node
/**
 * Test script for Robot Data API
 */

const http = require('http');

async function testRobotDataAPI() {
  console.log('🧪 Testing Robot Data API (Updated Robots Module)');
  console.log('=' .repeat(50));

  // Test data
  const testData = {
    ip_address: '*************',
    port: 9090
  };

  const postData = JSON.stringify(testData);

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/v1/robots/data/retrieve',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };
  
  console.log(`📡 Testing: POST ${options.hostname}:${options.port}${options.path}`);
  console.log(`📊 Request body: ${postData}`);
  console.log();
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📈 Status Code: ${res.statusCode}`);
        console.log(`📋 Response:`);
        
        try {
          const jsonResponse = JSON.parse(data);
          console.log(JSON.stringify(jsonResponse, null, 2));
          resolve(jsonResponse);
        } catch (e) {
          console.log(data);
          resolve(data);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error(`❌ Request error: ${error.message}`);
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check (Note: May require auth)');
  console.log('-' .repeat(30));

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/v1/robots/data/health',
    method: 'GET'
  };
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📈 Status Code: ${res.statusCode}`);
        console.log(`📋 Response: ${data}`);
        resolve(data);
      });
    });
    
    req.on('error', (error) => {
      console.error(`❌ Request error: ${error.message}`);
      reject(error);
    });
    
    req.end();
  });
}

async function main() {
  try {
    // Test health check first
    await testHealthCheck();
    
    // Test main API
    await testRobotDataAPI();
    
    console.log('\n✅ API tests completed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

main();
