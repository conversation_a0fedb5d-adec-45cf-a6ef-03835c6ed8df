# NestJS Firebase Backend - Robot Disinfection Management System

A pure NestJS backend application with Firebase integration for managing robot disinfection operations.

## 🏗️ Architecture

\`\`\`
src/
├── common/                 # Shared utilities, guards, decorators
│   ├── decorators/        # Custom decorators (roles, etc.)
│   └── guards/            # Authentication and authorization guards
├── config/                # Configuration modules
│   └── firebase/          # Firebase configuration and service
├── modules/               # Feature modules
│   ├── auth/             # Authentication module
│   ├── users/            # User management
│   ├── tasks/            # Task management
│   ├── robots/           # Robot management
│   ├── buildings/        # Building management
│   ├── floors/           # Floor management
│   ├── rooms/            # Room management
│   ├── zones/            # Zone management
│   └── history/          # History and reporting
├── app.module.ts         # Root application module
└── main.ts              # Application entry point
\`\`\`

## 🚀 Features

- **Firebase Integration**: Complete Firebase Admin SDK integration
- **Authentication**: JWT-based authentication with Firebase Auth
- **Authorization**: Role-based access control (User/Admin)
- **CRUD Operations**: Full CRUD for all entities
- **Data Validation**: Input validation using class-validator
- **API Documentation**: Auto-generated Swagger documentation
- **Modular Architecture**: Scalable and maintainable structure

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Firebase project with Firestore enabled
- Firebase Admin SDK service account key

## ⚙️ Installation

1. **Clone and install dependencies**:
\`\`\`bash
git clone <repository-url>
cd nestjs-firebase-backend
npm install
\`\`\`

2. **Configure environment variables**:
\`\`\`bash
cp .env.example .env
\`\`\`

3. **Set up Firebase credentials in `.env`**:
   - Create a Firebase project
   - Enable Firestore database
   - Generate a service account key
   - Fill in the Firebase configuration variables

## 🔥 Firebase Setup

1. **Create Firebase Project**:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project
   - Enable Firestore Database

2. **Generate Service Account Key**:
   - Go to Project Settings → Service Accounts
   - Click "Generate new private key"
   - Download the JSON file
   - Extract the values for your `.env` file

3. **Configure Authentication**:
   - Enable Authentication in Firebase Console
   - Set up sign-in methods (Email/Password recommended)

## 🏃‍♂️ Running the Application

### Development
\`\`\`bash
npm run start:dev
\`\`\`

### Production
\`\`\`bash
npm run build
npm run start:prod
\`\`\`

The application will be available at:
- **API**: `http://localhost:3001/api/v1`
- **Swagger Documentation**: `http://localhost:3001/api/docs`

## 📚 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `GET /api/v1/auth/profile` - Get user profile

### Users
- `GET /api/v1/users` - Get all users (Admin only)
- `GET /api/v1/users/:id` - Get user by ID
- `PATCH /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user (Admin only)

### Tasks
- `POST /api/v1/tasks` - Create new task
- `GET /api/v1/tasks` - Get all tasks
- `GET /api/v1/tasks/:id` - Get task by ID
- `PATCH /api/v1/tasks/:id` - Update task
- `PATCH /api/v1/tasks/:id/status` - Update task status
- `DELETE /api/v1/tasks/:id` - Delete task

### Robots
- `POST /api/v1/robots` - Register new robot (Admin only)
- `GET /api/v1/robots` - Get all robots
- `GET /api/v1/robots/:id` - Get robot by ID
- `PATCH /api/v1/robots/:id` - Update robot (Admin only)
- `PATCH /api/v1/robots/:id/status` - Update robot status
- `DELETE /api/v1/robots/:id` - Delete robot (Admin only)

### Buildings/Floors/Rooms/Zones
- Similar CRUD operations for hierarchical structure management

### History
- `POST /api/v1/history` - Log new history record
- `GET /api/v1/history` - Get history records
- `GET /api/v1/history/reports` - Generate reports (Admin only)
- `GET /api/v1/history/export` - Export data (Admin only)

## 🔐 Authentication

The API uses Firebase Authentication with Bearer tokens:

\`\`\`
Authorization: Bearer <firebase-id-token>
\`\`\`

## 👥 Role-Based Access Control

- **User**: Basic operations (create tasks, view own data)
- **Admin**: Full system access (user management, system configuration)

## 🗄️ Data Models

Based on your ER diagram:

- **User**: User accounts and profiles
- **Task**: Disinfection and maintenance tasks
- **Robot**: Robot fleet management
- **Building/Floor/Room/Zone**: Hierarchical location structure
- **History**: Comprehensive activity logging

## 🧪 Testing

\`\`\`bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
\`\`\`

## 🚀 Deployment

### Environment Variables for Production

\`\`\`env
NODE_ENV=production
PORT=3001
FIREBASE_PROJECT_ID=your-production-project-id
# ... other Firebase credentials
\`\`\`

## 🔒 Security Features

- Firebase Authentication integration
- Role-based access control
- Input validation using class-validator
- Environment variables for sensitive configuration
- Protected API endpoints

## 📊 Monitoring and Logging

- Comprehensive history logging for all operations
- Error handling and logging
- Export functionality for data analysis
- Admin reporting capabilities

## 🔧 Development

The modular architecture supports easy addition of:
- New entity modules
- Additional authentication providers
- Real-time features
- Advanced analytics
- Integration with external services

## 📄 License

This project is licensed under the MIT License.
