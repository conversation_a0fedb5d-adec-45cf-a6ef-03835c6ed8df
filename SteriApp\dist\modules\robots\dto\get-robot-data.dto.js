"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RobotDataResponseDto = exports.RobotBatteryDto = exports.RobotPositionDto = exports.GetRobotDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GetRobotDataDto {
}
exports.GetRobotDataDto = GetRobotDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address of the ROS2 bridge to connect to',
        example: '*************',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIP)(),
    __metadata("design:type", String)
], GetRobotDataDto.prototype, "ip_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port of the ROS2 bridge (optional, defaults to 8765)',
        example: 8765,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetRobotDataDto.prototype, "port", void 0);
class RobotPositionDto {
}
exports.RobotPositionDto = RobotPositionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'X coordinate of robot position',
        example: 1.25,
    }),
    __metadata("design:type", Number)
], RobotPositionDto.prototype, "x", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Y coordinate of robot position',
        example: -0.75,
    }),
    __metadata("design:type", Number)
], RobotPositionDto.prototype, "y", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Z coordinate of robot position',
        example: 0.0,
    }),
    __metadata("design:type", Number)
], RobotPositionDto.prototype, "z", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Robot orientation quaternion',
        type: 'object',
        properties: {
            x: { type: 'number', example: 0.0 },
            y: { type: 'number', example: 0.0 },
            z: { type: 'number', example: 0.707 },
            w: { type: 'number', example: 0.707 },
        },
    }),
    __metadata("design:type", Object)
], RobotPositionDto.prototype, "orientation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source ROS2 topic for position data',
        example: '/odom',
    }),
    __metadata("design:type", String)
], RobotPositionDto.prototype, "source_topic", void 0);
class RobotBatteryDto {
}
exports.RobotBatteryDto = RobotBatteryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Battery percentage (0-100)',
        example: 85.5,
        required: false,
    }),
    __metadata("design:type", Number)
], RobotBatteryDto.prototype, "percentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Battery voltage in volts',
        example: 12.6,
        required: false,
    }),
    __metadata("design:type", Number)
], RobotBatteryDto.prototype, "voltage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Battery current in amperes',
        example: -2.1,
        required: false,
    }),
    __metadata("design:type", Number)
], RobotBatteryDto.prototype, "current", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source ROS2 topic for battery data',
        example: '/battery_state',
    }),
    __metadata("design:type", String)
], RobotBatteryDto.prototype, "source_topic", void 0);
class RobotDataResponseDto {
}
exports.RobotDataResponseDto = RobotDataResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current position of the robot',
        type: RobotPositionDto,
        required: false,
    }),
    __metadata("design:type", RobotPositionDto)
], RobotDataResponseDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current battery level of the robot',
        type: RobotBatteryDto,
        required: false,
    }),
    __metadata("design:type", RobotBatteryDto)
], RobotDataResponseDto.prototype, "battery_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Connection status to the ROS2 bridge',
        enum: ['connected', 'failed', 'timeout', 'disconnected'],
        example: 'connected',
    }),
    __metadata("design:type", String)
], RobotDataResponseDto.prototype, "connection_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when data was last updated',
        example: '2025-07-28T14:30:00.000Z',
    }),
    __metadata("design:type", String)
], RobotDataResponseDto.prototype, "last_updated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message if connection failed',
        example: 'Connection timeout',
        required: false,
    }),
    __metadata("design:type", String)
], RobotDataResponseDto.prototype, "error_message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IP address that was queried',
        example: '*************',
    }),
    __metadata("design:type", String)
], RobotDataResponseDto.prototype, "queried_ip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Port that was queried',
        example: 8765,
    }),
    __metadata("design:type", Number)
], RobotDataResponseDto.prototype, "queried_port", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether Foxglove SDK is enabled for data logging',
        example: true,
    }),
    __metadata("design:type", Boolean)
], RobotDataResponseDto.prototype, "foxglove_enabled", void 0);
//# sourceMappingURL=get-robot-data.dto.js.map