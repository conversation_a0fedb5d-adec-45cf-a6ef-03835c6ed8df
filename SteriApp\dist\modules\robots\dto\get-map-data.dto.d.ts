export declare class GetMapDataDto {
    ip_address: string;
    port?: number;
}
export declare class MapOriginDto {
    position: {
        x: number;
        y: number;
        z: number;
    };
    orientation: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
}
export declare class MapInfoDto {
    width: number;
    height: number;
    resolution: number;
    origin: MapOriginDto;
    map_load_time: string;
}
export declare class MapStatisticsDto {
    total_cells: number;
    free_cells: number;
    occupied_cells: number;
    unknown_cells: number;
    free_percentage: number;
    occupied_percentage: number;
    unknown_percentage: number;
}
export declare class MapDataResponseDto {
    info?: MapInfoDto;
    data?: number[];
    statistics?: MapStatisticsDto;
    connection_status: string;
    last_updated: string;
    error_message?: string;
    queried_ip: string;
    queried_port: number;
    has_map_data: boolean;
    data_length: number;
    summary?: string;
}
