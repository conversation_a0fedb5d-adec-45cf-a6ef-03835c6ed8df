import { DepartmentsService } from "./departments.service";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { UpdateDepartmentDto } from "./dto/update-department.dto";
export declare class DepartmentsController {
    private readonly departmentsService;
    constructor(departmentsService: DepartmentsService);
    create(createDepartmentDto: CreateDepartmentDto): Promise<{
        createdAt: Date;
        updatedAt: Date;
        name: string;
        description?: string;
        parentDepartmentId?: string;
        code?: string;
        id: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    findChildren(id: string): Promise<{
        id: string;
    }[]>;
    update(id: string, updateDepartmentDto: UpdateDepartmentDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
