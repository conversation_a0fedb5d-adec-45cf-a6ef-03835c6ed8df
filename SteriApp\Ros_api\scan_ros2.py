import socket
import concurrent.futures
from functools import partial

def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(("*******", 80))
        return s.getsockname()[0]
    except:
        return "***********"
    finally:
        s.close()

def scan_ip(ip, port, timeout):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(timeout)
    result = s.connect_ex((ip, port))
    s.close()
    return ip if result == 0 else None

def scan_network(port, workers, timeout):
    prefix = ".".join(get_local_ip().split(".")[:3])
    ips = [f"{prefix}.{i}" for i in range(1, 255)]
    scanner = partial(scan_ip, port=port, timeout=timeout)
    found = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as e:
        for r in e.map(scanner, ips):
            if r: found.append(r)
    return found

if __name__ == "__main__":
    for ip in scan_network(9090, 150, 0.08):
        print(f"ws://{ip}:9090")