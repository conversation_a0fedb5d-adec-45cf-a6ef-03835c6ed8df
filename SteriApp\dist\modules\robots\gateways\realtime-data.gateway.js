"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RealtimeDataGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeDataGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
const realtime_data_dto_1 = require("../dto/realtime-data.dto");
const realtime_data_service_1 = require("../services/realtime-data.service");
let RealtimeDataGateway = RealtimeDataGateway_1 = class RealtimeDataGateway {
    constructor(realtimeDataService) {
        this.realtimeDataService = realtimeDataService;
        this.logger = new common_1.Logger(RealtimeDataGateway_1.name);
        this.connectedClients = new Map();
        this.clientStreams = new Map();
    }
    afterInit(server) {
        this.logger.log('🚀 Realtime Data WebSocket Gateway initialized');
        this.realtimeDataService.onDataReceived((eventType, data) => {
            this.broadcastToSubscribers(eventType, data);
        });
    }
    handleConnection(client) {
        const clientId = client.id;
        this.connectedClients.set(clientId, client);
        this.clientStreams.set(clientId, new Set());
        this.logger.log(`🔌 Client connected: ${clientId}`);
        client.emit('connection', {
            event: 'connected',
            client_id: clientId,
            timestamp: new Date().toISOString(),
            message: 'Connected to SteriBot Realtime Data Gateway',
        });
        const activeStreams = this.realtimeDataService.getActiveStreams();
        client.emit('active_streams', {
            event: 'active_streams',
            streams: activeStreams,
            timestamp: new Date().toISOString(),
        });
    }
    handleDisconnect(client) {
        const clientId = client.id;
        this.logger.log(`🔌 Client disconnected: ${clientId}`);
        const clientStreamIds = this.clientStreams.get(clientId);
        if (clientStreamIds) {
            for (const sessionId of clientStreamIds) {
                this.realtimeDataService.stopStream(sessionId);
            }
        }
        this.connectedClients.delete(clientId);
        this.clientStreams.delete(clientId);
    }
    async handleStartStream(data, client) {
        try {
            this.logger.log(`📡 Starting stream for client ${client.id}: ${data.ip_address}:${data.port || 8765}`);
            const config = {
                ip_address: data.ip_address,
                port: data.port || 8765,
                include_robot_data: data.include_robot_data ?? true,
                include_map_data: data.include_map_data ?? true,
                update_frequency: data.update_frequency || 2,
                session_id: `stream_${data.ip_address}_${data.port || 8765}_${Date.now()}`,
            };
            const result = await this.realtimeDataService.startStream(config);
            if (result.success) {
                const clientStreams = this.clientStreams.get(client.id);
                if (clientStreams) {
                    clientStreams.add(config.session_id);
                }
                client.emit('stream_started', {
                    event: 'stream_started',
                    session_id: config.session_id,
                    config: config,
                    timestamp: new Date().toISOString(),
                    success: true,
                });
                this.logger.log(`✅ Stream started successfully: ${config.session_id}`);
            }
            else {
                client.emit('stream_error', {
                    event: 'stream_error',
                    error: result.error,
                    timestamp: new Date().toISOString(),
                    success: false,
                });
                this.logger.error(`❌ Failed to start stream: ${result.error}`);
            }
        }
        catch (error) {
            this.logger.error(`❌ Error starting stream: ${error.message}`);
            client.emit('stream_error', {
                event: 'stream_error',
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false,
            });
        }
    }
    async handleStopStream(data, client) {
        try {
            this.logger.log(`🛑 Stopping stream for client ${client.id}: ${data.session_id}`);
            const result = await this.realtimeDataService.stopStream(data.session_id);
            const clientStreams = this.clientStreams.get(client.id);
            if (clientStreams) {
                clientStreams.delete(data.session_id);
            }
            client.emit('stream_stopped', {
                event: 'stream_stopped',
                session_id: data.session_id,
                timestamp: new Date().toISOString(),
                success: result.success,
                message: result.success ? 'Stream stopped successfully' : result.error,
            });
            this.logger.log(`✅ Stream stopped: ${data.session_id}`);
        }
        catch (error) {
            this.logger.error(`❌ Error stopping stream: ${error.message}`);
            client.emit('stream_error', {
                event: 'stream_error',
                session_id: data.session_id,
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false,
            });
        }
    }
    handleGetStreamStatus(client) {
        try {
            const activeStreams = this.realtimeDataService.getActiveStreams();
            const clientStreams = this.clientStreams.get(client.id) || new Set();
            client.emit('stream_status', {
                event: 'stream_status',
                active_streams: activeStreams,
                client_streams: Array.from(clientStreams),
                total_active: activeStreams.length,
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            this.logger.error(`❌ Error getting stream status: ${error.message}`);
            client.emit('stream_error', {
                event: 'stream_error',
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false,
            });
        }
    }
    handleSubscribeToStream(data, client) {
        try {
            const { session_id } = data;
            const clientStreams = this.clientStreams.get(client.id);
            if (clientStreams) {
                clientStreams.add(session_id);
            }
            client.emit('subscribed_to_stream', {
                event: 'subscribed_to_stream',
                session_id: session_id,
                timestamp: new Date().toISOString(),
                success: true,
            });
            this.logger.log(`📡 Client ${client.id} subscribed to stream: ${session_id}`);
        }
        catch (error) {
            this.logger.error(`❌ Error subscribing to stream: ${error.message}`);
            client.emit('stream_error', {
                event: 'stream_error',
                error: error.message,
                timestamp: new Date().toISOString(),
                success: false,
            });
        }
    }
    broadcastToSubscribers(eventType, data) {
        try {
            for (const [clientId, client] of this.connectedClients) {
                const clientStreams = this.clientStreams.get(clientId);
                if (clientStreams && 'session_id' in data && clientStreams.has(data.session_id)) {
                    client.emit(eventType, data);
                }
            }
        }
        catch (error) {
            this.logger.error(`❌ Error broadcasting to subscribers: ${error.message}`);
        }
    }
    broadcastData(eventType, data) {
        this.broadcastToSubscribers(eventType, data);
    }
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    getClientStreams(clientId) {
        const streams = this.clientStreams.get(clientId);
        return streams ? Array.from(streams) : [];
    }
};
exports.RealtimeDataGateway = RealtimeDataGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], RealtimeDataGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('start_stream'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [realtime_data_dto_1.StartRealtimeStreamDto,
        socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], RealtimeDataGateway.prototype, "handleStartStream", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('stop_stream'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [realtime_data_dto_1.StopRealtimeStreamDto,
        socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], RealtimeDataGateway.prototype, "handleStopStream", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('get_stream_status'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], RealtimeDataGateway.prototype, "handleGetStreamStatus", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe_to_stream'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], RealtimeDataGateway.prototype, "handleSubscribeToStream", null);
exports.RealtimeDataGateway = RealtimeDataGateway = RealtimeDataGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: ['http://localhost:3000', 'http://localhost:3005'],
            methods: ['GET', 'POST'],
            credentials: true,
        },
        namespace: '/realtime-robot-data',
    }),
    __metadata("design:paramtypes", [realtime_data_service_1.RealtimeDataService])
], RealtimeDataGateway);
//# sourceMappingURL=realtime-data.gateway.js.map