import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateDepartmentDto } from "./dto/create-department.dto";
import type { UpdateDepartmentDto } from "./dto/update-department.dto";
export declare class DepartmentsService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createDepartmentDto: CreateDepartmentDto): Promise<{
        createdAt: Date;
        updatedAt: Date;
        name: string;
        description?: string;
        parentDepartmentId?: string;
        code?: string;
        id: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    findByName(name: string): Promise<{
        id: string;
    }>;
    findChildren(parentId: string): Promise<{
        id: string;
    }[]>;
    update(id: string, updateDepartmentDto: UpdateDepartmentDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
