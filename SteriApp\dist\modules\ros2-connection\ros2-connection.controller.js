"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROS2ConnectionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ros2_connection_service_1 = require("./ros2-connection.service");
let ROS2ConnectionController = class ROS2ConnectionController {
    constructor(ros2) {
        this.ros2 = ros2;
    }
    async connect() {
        return this.ros2.connectToROS2IPs();
    }
    healthCheck() {
        return this.ros2.healthCheck();
    }
};
exports.ROS2ConnectionController = ROS2ConnectionController;
__decorate([
    (0, common_1.Get)('connect-ros2'),
    (0, swagger_1.ApiOperation)({ summary: 'Test ROS2 WebSocket connections' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        schema: {
            type: 'object',
            properties: {
                ips: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            ip: { type: 'string' },
                            status: { type: 'string', enum: ['connected', 'failed'] }
                        }
                    }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ROS2ConnectionController.prototype, "connect", null);
__decorate([
    (0, common_1.Get)('ros2-health'),
    (0, swagger_1.ApiOperation)({ summary: 'ROS2 service health check' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string' },
                scan_script_exists: { type: 'boolean' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ROS2ConnectionController.prototype, "healthCheck", null);
exports.ROS2ConnectionController = ROS2ConnectionController = __decorate([
    (0, swagger_1.ApiTags)('ROS2'),
    (0, common_1.Controller)('api'),
    __metadata("design:paramtypes", [ros2_connection_service_1.ROS2ConnectionService])
], ROS2ConnectionController);
//# sourceMappingURL=ros2-connection.controller.js.map