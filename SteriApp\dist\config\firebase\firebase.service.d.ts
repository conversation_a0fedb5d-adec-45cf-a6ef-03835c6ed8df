import { type OnModuleInit } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as admin from "firebase-admin";
export declare class FirebaseService implements OnModuleInit {
    private configService;
    private firestore;
    private auth;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    getFirestore(): admin.firestore.Firestore;
    getAuth(): admin.auth.Auth;
}
