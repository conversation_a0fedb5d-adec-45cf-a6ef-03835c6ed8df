"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloorsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const floors_service_1 = require("./floors.service");
const create_floor_dto_1 = require("./dto/create-floor.dto");
const update_floor_dto_1 = require("./dto/update-floor.dto");
let FloorsController = class FloorsController {
    constructor(floorsService) {
        this.floorsService = floorsService;
    }
    create(createFloorDto) {
        return this.floorsService.create(createFloorDto);
    }
    findAll(buildingId) {
        if (buildingId) {
            return this.floorsService.findByBuilding(buildingId);
        }
        return this.floorsService.findAll();
    }
    findOne(id) {
        return this.floorsService.findById(id);
    }
    update(id, updateFloorDto) {
        return this.floorsService.update(id, updateFloorDto);
    }
    remove(id) {
        return this.floorsService.remove(id);
    }
};
exports.FloorsController = FloorsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Create a new floor" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_floor_dto_1.CreateFloorDto]),
    __metadata("design:returntype", void 0)
], FloorsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all floors' }),
    (0, swagger_1.ApiQuery)({ name: 'buildingId', required: false }),
    __param(0, (0, common_1.Query)('buildingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FloorsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get floor by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FloorsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "Update floor" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_floor_dto_1.UpdateFloorDto]),
    __metadata("design:returntype", void 0)
], FloorsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete floor' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FloorsController.prototype, "remove", null);
exports.FloorsController = FloorsController = __decorate([
    (0, swagger_1.ApiTags)("Floors"),
    (0, common_1.Controller)("floors"),
    __metadata("design:paramtypes", [floors_service_1.FloorsService])
], FloorsController);
//# sourceMappingURL=floors.controller.js.map