"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
const users_service_1 = require("../users/users.service");
const job_titles_service_1 = require("../job-titles/job-titles.service");
const departments_service_1 = require("../departments/departments.service");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let AuthService = class AuthService {
    constructor(firebaseService, usersService, jobTitlesService, departmentsService) {
        this.firebaseService = firebaseService;
        this.usersService = usersService;
        this.jobTitlesService = jobTitlesService;
        this.departmentsService = departmentsService;
    }
    async register(registerDto) {
        try {
            console.log('Starting user registration for:', registerDto.email);
            if (registerDto.jobTitleId) {
                try {
                    await this.jobTitlesService.findById(registerDto.jobTitleId);
                }
                catch (error) {
                    throw new common_1.BadRequestException(`Job title with ID ${registerDto.jobTitleId} not found`);
                }
            }
            if (registerDto.departmentId) {
                try {
                    await this.departmentsService.findById(registerDto.departmentId);
                }
                catch (error) {
                    throw new common_1.BadRequestException(`Department with ID ${registerDto.departmentId} not found`);
                }
            }
            const userRecord = await this.firebaseService.getAuth().createUser({
                email: registerDto.email,
                password: registerDto.password,
                displayName: registerDto.username,
            });
            console.log('Firebase user created successfully:', userRecord.uid);
            const userData = {
                userId: userRecord.uid,
                username: registerDto.username,
                firstName: registerDto.firstName,
                lastName: registerDto.lastName,
                email: registerDto.email,
                role: registerDto.role || roles_decorator_1.UserRole.USER,
                language: registerDto.language || "en",
                jobTitleId: registerDto.jobTitleId,
                departmentId: registerDto.departmentId,
                picture: registerDto.picture,
                createdAt: new Date().toISOString(),
                lastLogin: null,
            };
            await this.usersService.create(userData);
            console.log('User document created in Firestore');
            return {
                message: "User registered successfully",
                userId: userRecord.uid,
            };
        }
        catch (error) {
            console.error('Registration error:', error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            if (error.code === 'auth/email-already-exists') {
                throw new common_1.UnauthorizedException('Email already exists');
            }
            else if (error.code === 'auth/invalid-email') {
                throw new common_1.UnauthorizedException('Invalid email address');
            }
            else if (error.code === 'auth/weak-password') {
                throw new common_1.UnauthorizedException('Password is too weak');
            }
            else if (error.message?.includes('certificate')) {
                throw new common_1.UnauthorizedException('Network connection issue. Please check your internet connection or contact your network administrator.');
            }
            throw new common_1.UnauthorizedException(`Registration failed: ${error.message}`);
        }
    }
    async validateUser(uid) {
        try {
            const userData = await this.usersService.findById(uid);
            return this.usersService.createProfileResponse(userData, this.jobTitlesService, this.departmentsService);
        }
        catch (error) {
            throw new common_1.UnauthorizedException("User validation failed");
        }
    }
    async login(loginDto) {
        try {
            console.log('Starting user login for:', loginDto.email);
            await this.verifyEmailPassword(loginDto.email, loginDto.password);
            const userData = await this.usersService.findByEmail(loginDto.email);
            if (!userData) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const customToken = await this.firebaseService.getAuth().createCustomToken(userData.id, {
                role: userData.role,
                email: userData.email,
                username: userData.username
            });
            console.log('Login successful for user:', userData.id);
            return {
                message: "Login successful",
                customToken: customToken,
                instructions: "Use this custom token to authenticate with Firebase Auth SDK to get an ID token, or use the test endpoint below",
                user: {
                    userId: userData.id,
                    email: userData.email,
                    username: userData.username,
                    role: userData.role
                }
            };
        }
        catch (error) {
            console.error('Login error:', error);
            if (error.message?.includes('INVALID_PASSWORD') || error.message?.includes('EMAIL_NOT_FOUND')) {
                throw new common_1.UnauthorizedException('Invalid email or password');
            }
            else if (error.message?.includes('TOO_MANY_ATTEMPTS_TRY_LATER')) {
                throw new common_1.UnauthorizedException('Too many failed login attempts. Please try again later.');
            }
            else if (error.message?.includes('USER_DISABLED')) {
                throw new common_1.UnauthorizedException('This account has been disabled');
            }
            throw new common_1.UnauthorizedException(`Login failed: ${error.message}`);
        }
    }
    async verifyEmailPassword(email, password) {
        try {
            const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${process.env.FIREBASE_WEB_API_KEY}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                    returnSecureToken: true,
                }),
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error?.message || 'Authentication failed');
            }
            console.log('Password verification successful for:', email);
        }
        catch (error) {
            console.error('Password verification failed:', error);
            throw error;
        }
    }
    async updateLastLogin(userId) {
        await this.usersService.updateLastLogin(userId);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService,
        users_service_1.UsersService,
        job_titles_service_1.JobTitlesService,
        departments_service_1.DepartmentsService])
], AuthService);
//# sourceMappingURL=auth.service.js.map