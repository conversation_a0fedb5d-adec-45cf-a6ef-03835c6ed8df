#!/usr/bin/env python3
"""
Robot Data Bridge for SteriBot
Uses proper Foxglove SDK with ServerListener and Channels
Connects to ROS2 bridge and forwards data to SteriBot backend
Compatible with Foxglove Studio
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime
from typing import Dict, Any, Set
import time

try:
    import foxglove
    from foxglove.websocket import (
        Capability,
        ChannelView,
        Client,
        ClientChannel,
        ServerListener,
    )
    FOXGLOVE_AVAILABLE = True
except ImportError:
    FOXGLOVE_AVAILABLE = False
    print("❌ Foxglove SDK not available. Install with: pip install foxglove-sdk")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SteriBotFoxgloveListener(ServerListener):
    """
    Foxglove ServerListener that handles client connections and forwards data to SteriBot backend.
    Uses proper Foxglove SDK with Channels and data logging.
    """
    
    def __init__(self, ros2_bridge_url="ws://*************:8765"):
        # Track client subscriptions
        self.subscribers: Dict[int, Set[str]] = {}
        
        # ROS2 connection
        self.ros2_bridge_url = ros2_bridge_url
        self.ros2_websocket = None
        self.ros2_connected = False
        
        # Data storage for latest values (excluding problematic TF topics)
        self.latest_data = {
            "/cmd_vel": None,
            "/imu": None,
            "/joint_states": None,
            "/odom": None,
            "/scan": None,
            # "/tf": None,  # Removed due to CBOR serialization issues
            # "/tf_static": None,  # Removed due to CBOR serialization issues
            "/map": None  # For 2D map visualization
        }
        
        # Statistics
        self.messages_received = 0
        self.messages_logged = 0
        self.data_forwarded = 0
        self.start_time = datetime.now()
        
        logger.info("🤖 SteriBot Foxglove Listener initialized")
        logger.info(f"🔗 ROS2 Bridge: {self.ros2_bridge_url}")
        logger.info("📡 Ready to handle Foxglove Studio connections")

    def has_subscribers(self) -> bool:
        """Check if any Foxglove clients are connected."""
        return len(self.subscribers) > 0

    def on_subscribe(self, client: Client, channel: ChannelView) -> None:
        """Called when Foxglove Studio subscribes to a channel."""
        topic = channel.topic
        logger.info(f"📡 Client {client.id} subscribed to: {topic}")
        
        # Track subscription
        self.subscribers.setdefault(client.id, set()).add(topic)
        
        # If this is the first subscriber, start ROS2 connection
        if len(self.subscribers) == 1 and not self.ros2_connected:
            logger.info("🚀 First client connected - starting ROS2 connection")
            asyncio.create_task(self.connect_to_ros2())

    def on_unsubscribe(self, client: Client, channel: ChannelView) -> None:
        """Called when Foxglove Studio unsubscribes from a channel."""
        topic = channel.topic
        logger.info(f"📡 Client {client.id} unsubscribed from: {topic}")
        
        # Remove subscription
        if client.id in self.subscribers:
            self.subscribers[client.id].discard(topic)
            if not self.subscribers[client.id]:
                del self.subscribers[client.id]

    def on_client_advertise(self, client: Client, channel: ClientChannel) -> None:
        """Called when Foxglove Studio advertises a channel."""
        logger.info(f"📢 Client {client.id} advertised: {channel.topic}")
        logger.debug(f"   Schema: {channel.schema_name}")
        logger.debug(f"   Encoding: {channel.encoding}")

    def on_message_data(self, client: Client, client_channel_id: int, data: bytes) -> None:
        """Called when Foxglove Studio sends data (e.g., from publish panel)."""
        logger.info(f"📨 Message from client {client.id} on channel {client_channel_id}")
        
        try:
            # Try to decode as JSON
            message_data = json.loads(data.decode('utf-8'))
            logger.debug(f"   Data: {str(message_data)[:100]}...")
            
            # Forward to SteriBot backend
            self.forward_to_steribot("client_message", message_data, client_channel_id)
            
        except (json.JSONDecodeError, UnicodeDecodeError):
            logger.debug(f"   Binary data: {len(data)} bytes")

    def on_client_unadvertise(self, client: Client, client_channel_id: int) -> None:
        """Called when Foxglove Studio stops advertising a channel."""
        logger.info(f"📢 Client {client.id} unadvertised channel: {client_channel_id}")

    async def connect_to_ros2(self):
        """Connect to ROS2 bridge and start listening for data."""
        try:
            logger.info(f"🔗 Connecting to ROS2 bridge: {self.ros2_bridge_url}")
            
            self.ros2_websocket = await websockets.connect(self.ros2_bridge_url)
            self.ros2_connected = True
            
            logger.info("✅ Connected to ROS2 bridge!")
            
            # Subscribe to ROS2 topics
            await self.subscribe_to_ros2_topics()
            
            # Start listening for ROS2 data
            await self.listen_to_ros2_data()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to ROS2: {e}")
            self.ros2_connected = False

    async def subscribe_to_ros2_topics(self):
        """Subscribe to ROS2 topics using rosbridge protocol."""
        # Remove TF topics that cause CBOR serialization errors
        topics_with_types = [
            ("/cmd_vel", "geometry_msgs/msg/Twist"),
            ("/imu", "sensor_msgs/msg/Imu"),
            ("/joint_states", "sensor_msgs/msg/JointState"),
            ("/odom", "nav_msgs/msg/Odometry"),
            ("/scan", "sensor_msgs/msg/LaserScan"),
            # ("/tf", "tf2_msgs/msg/TFMessage"),  # Causes CBOR errors
            # ("/tf_static", "tf2_msgs/msg/TFMessage"),  # Causes CBOR errors
            ("/map", "nav_msgs/msg/OccupancyGrid")  # For 2D map visualization
        ]
        
        for topic, msg_type in topics_with_types:
            subscribe_msg = {
                "op": "subscribe",
                "topic": topic,
                "type": msg_type,
                "throttle_rate": 100  # 10 Hz max
            }
            await self.ros2_websocket.send(json.dumps(subscribe_msg))
            logger.info(f"📡 Subscribed to ROS2: {topic} ({msg_type})")

    async def listen_to_ros2_data(self):
        """Listen for ROS2 data and log it to Foxglove channels."""
        logger.info("👂 Listening for ROS2 data...")
        
        try:
            async for message in self.ros2_websocket:
                await self.process_ros2_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ ROS2 connection closed")
            self.ros2_connected = False
        except Exception as e:
            logger.error(f"❌ Error listening to ROS2: {e}")

    async def process_ros2_message(self, message):
        """Process ROS2 message and log to Foxglove using proper channels."""
        try:
            data = json.loads(message)
            
            if "topic" in data and "msg" in data:
                topic = data["topic"]
                msg = data["msg"]
                
                self.messages_received += 1
                
                # Store latest data
                self.latest_data[topic] = {
                    "timestamp": datetime.now().isoformat(),
                    "data": msg
                }
                
                # Only process if we have subscribers for this topic
                if self.has_subscribers() and any(topic in topics for topics in self.subscribers.values()):
                    
                    # 🎯 PROPER FOXGLOVE WAY: Use foxglove.log() to publish data
                    foxglove_data = {
                        "timestamp": time.time(),
                        "robot_id": "ros2_robot_001",
                        "topic": topic,
                        "data": msg
                    }
                    
                    # Log using Foxglove SDK
                    foxglove.log(topic, foxglove_data)
                    self.messages_logged += 1
                    
                    # Forward to SteriBot backend
                    self.forward_to_steribot(topic, msg, None)
                    
                    if self.messages_logged % 50 == 0:
                        logger.info(f"📊 Logged {self.messages_logged} messages to Foxglove")
                
        except json.JSONDecodeError:
            pass  # Ignore non-JSON messages
        except Exception as e:
            logger.error(f"❌ Error processing ROS2 message: {e}")

    def forward_to_steribot(self, topic, data, channel_id):
        """Forward data to SteriBot backend."""
        try:
            steribot_data = {
                "source": "foxglove_bridge",
                "topic": topic,
                "timestamp": datetime.now().isoformat(),
                "robot_id": "ros2_robot_001",
                "data": data,
                "channel_id": channel_id
            }
            
            self.data_forwarded += 1
            
            # TODO: Send to SteriBot backend
            # import requests
            # response = requests.post(
            #     "http://localhost:3001/api/v1/robots/foxglove/data",
            #     json=steribot_data,
            #     timeout=5
            # )
            
            logger.debug(f"📤 Would forward {topic} to SteriBot backend")
            
        except Exception as e:
            logger.error(f"❌ Failed to forward to SteriBot: {e}")

    def get_stats(self):
        """Get server statistics."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "ros2_connected": self.ros2_connected,
            "connected_clients": len(self.subscribers),
            "subscribed_topics": sum(len(topics) for topics in self.subscribers.values()),
            "messages_received": self.messages_received,
            "messages_logged": self.messages_logged,
            "data_forwarded": self.data_forwarded,
            "uptime": uptime
        }


async def main():
    """Main function using proper Foxglove SDK approach."""
    if not FOXGLOVE_AVAILABLE:
        print("❌ Foxglove SDK not available. Install with: pip install foxglove-sdk")
        return

    print("🤖 SteriBot Robot Data Bridge")
    print("=" * 50)
    print("🎯 Purpose: Proper Foxglove SDK with ServerListener")
    print("📡 Method: ROS2 → Foxglove Channels → Foxglove Studio + SteriBot Backend")
    print("🔄 Flow: Uses foxglove.log() for data publishing")
    print("🗺️ Map: 2D map visualization from /map topic")
    print()
    
    # Set up Foxglove logging
    foxglove.set_log_level(logging.INFO)
    
    # Create listener
    listener = SteriBotFoxgloveListener()
    
    # Start Foxglove server with proper capabilities
    server = foxglove.start_server(
        server_listener=listener,
        capabilities=[Capability.ClientPublish],
        supported_encodings=["json"],
        port=8768  # Use different port to avoid conflicts
    )

    print("✅ Foxglove server started on port 8768")
    print("🔗 Connect Foxglove Studio to: ws://localhost:8768")
    print()
    print("📋 How it works:")
    print("1. Foxglove Studio connects to this server")
    print("2. Server connects to your ROS2 bridge (*************:8765)")
    print("3. ROS2 data is logged using foxglove.log()")
    print("4. Data is forwarded to SteriBot backend")
    print("5. 2D map visualization available from /map topic")
    print()
    print("⏳ Waiting for Foxglove Studio to connect...")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            await asyncio.sleep(10)
            
            stats = listener.get_stats()
            if stats["connected_clients"] > 0:
                print(f"\n📊 Stats:")
                print(f"   ROS2 connected: {'✅' if stats['ros2_connected'] else '❌'}")
                print(f"   Foxglove clients: {stats['connected_clients']}")
                print(f"   Subscribed topics: {stats['subscribed_topics']}")
                print(f"   Messages received: {stats['messages_received']}")
                print(f"   Messages logged: {stats['messages_logged']}")
                print(f"   Data forwarded: {stats['data_forwarded']}")
            else:
                print("⏳ Waiting for Foxglove Studio to connect...")
                
    except KeyboardInterrupt:
        print("\n⏹️ Stopping server...")
        
        stats = listener.get_stats()
        print(f"\n📊 Final Stats:")
        print(f"   Messages processed: {stats['messages_received']}")
        print(f"   Messages logged: {stats['messages_logged']}")
        print(f"   Data forwarded: {stats['data_forwarded']}")
        
        server.stop()
        print("👋 Server stopped")


if __name__ == "__main__":
    asyncio.run(main())
