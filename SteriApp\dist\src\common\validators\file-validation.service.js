"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileValidationService = void 0;
const common_1 = require("@nestjs/common");
let FileValidationService = class FileValidationService {
    constructor() {
        this.allowedMimeTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/webp'
        ];
        this.maxFileSize = 5 * 1024 * 1024;
    }
    validateImageFile(file) {
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        if (file.size > this.maxFileSize) {
            throw new common_1.BadRequestException(`File size too large. Maximum allowed size is ${this.maxFileSize / (1024 * 1024)}MB`);
        }
        if (!this.allowedMimeTypes.includes(file.mimetype)) {
            throw new common_1.BadRequestException(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`);
        }
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
        const fileExtension = this.getFileExtension(file.originalname).toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            throw new common_1.BadRequestException(`Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`);
        }
    }
    generateUniqueFileName(userId, originalName) {
        const timestamp = Date.now();
        const extension = this.getFileExtension(originalName);
        return `${userId}_${timestamp}${extension}`;
    }
    getFileExtension(filename) {
        const lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
    }
    getContentTypeFromExtension(filename) {
        const extension = this.getFileExtension(filename).toLowerCase();
        switch (extension) {
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.png':
                return 'image/png';
            case '.webp':
                return 'image/webp';
            default:
                return 'application/octet-stream';
        }
    }
};
exports.FileValidationService = FileValidationService;
exports.FileValidationService = FileValidationService = __decorate([
    (0, common_1.Injectable)()
], FileValidationService);
//# sourceMappingURL=file-validation.service.js.map