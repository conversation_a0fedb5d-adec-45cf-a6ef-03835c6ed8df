#!/usr/bin/env python3
"""
Advanced ROS2 Visualizer - Compatible with your Foxglove SDK version
"""
import asyncio
import json
import logging
import websockets
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import time
import os
import foxglove  # Import the entire module first
from foxglove import Channel  # Then import specific components

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ros2_visualizer")

class CompatibleDataCollector:
    """Data collector compatible with your Foxglove SDK version."""
    def __init__(self, ros2_bridge_url="ws://192.168.1.165:8765"):
        self.ros2_bridge_url = ros2_bridge_url
        self.websocket = None
        self.connected = False
        # Data storage
        self.latest_data = {
            "/cmd_vel": None,
            "/imu": None,
            "/joint_states": None,
            "/odom": None,
            "/scan": None,
            "/map": None,
            "/tf": None,
            "/point_cloud": None
        }
        # Statistics
        self.messages_received = 0
        self.start_time = datetime.now()
        # Foxglove related
        self.foxglove_server = None
        logger.info("🚀 Compatible Data Collector initialized")
        logger.info(f"🔗 ROS2 Bridge: {self.ros2_bridge_url}")

    async def init_foxglove(self):
        """Initialize Foxglove using the correct API for your SDK version."""
        try:
            # Set log level
            foxglove.set_log_level(logging.INFO)
            
            # Start Foxglove server - this is the correct method for your SDK version
            self.foxglove_server = foxglove.start_server(
                name="ROS2 Visualizer",
                port=8772,
                description="ROS2 data visualization compatible with your Foxglove SDK version"
            )
            
            logger.info("📺 Foxglove server started on port 8772")
            logger.info("📡 Connect Foxglove Studio to: ws://localhost:8772")
            
            # Register channels - using the correct API
            self._register_channels()
            
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Foxglove: {str(e)}")
            return False

    def _register_channels(self):
        """Register channels using the correct API for your SDK version."""
        # Define channel schemas
        schemas = {
            "/cmd_vel": {
                "encoding": "json",
                "schema": json.dumps({
                    "type": "object",
                    "properties": {
                        "linear": {"type": "object", "properties": {"x": {"type": "number"}}},
                        "angular": {"type": "object", "properties": {"z": {"type": "number"}}}
                    }
                })
            },
            "/imu": {
                "encoding": "json",
                "schema": json.dumps({
                    "type": "object",
                    "properties": {
                        "orientation": {"type": "object"},
                        "angular_velocity": {"type": "object"},
                        "linear_acceleration": {"type": "object"}
                    }
                })
            },
            # Add other channels as needed...
        }
        
        # Register channels
        for topic, config in schemas.items():
            try:
                # This is the correct way to register channels in your SDK version
                foxglove.register_channel(
                    topic=topic,
                    encoding=config["encoding"],
                    schema=config["schema"]
                )
                logger.info(f"📡 Registered channel: {topic}")
            except Exception as e:
                logger.error(f"❌ Failed to register channel {topic}: {str(e)}")

    async def connect_and_listen(self):
        """Connect to ROS2 and start listening."""
        # Initialize Foxglove
        foxglove_initialized = await self.init_foxglove()
        
        while True:
            try:
                logger.info(f"🔗 Connecting to ROS2 bridge: {self.ros2_bridge_url}")
                self.websocket = await websockets.connect(self.ros2_bridge_url)
                self.connected = True
                logger.info("✅ Connected to ROS2 bridge!")
                # Subscribe to all topics
                await self.subscribe_to_topics()
                # Listen for messages
                logger.info("👂 Listening for ROS2 data...")
                async for message in self.websocket:
                    await self.process_message(message)
            except websockets.exceptions.ConnectionClosed:
                logger.warning("⚠️ ROS2 connection closed, reconnecting...")
                self.connected = False
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"❌ ROS2 connection error: {str(e)}")
                self.connected = False
                await asyncio.sleep(5)

    async def subscribe_to_topics(self):
        """Subscribe to ROS2 topics with proper throttling."""
        topics_with_types = [
            ("/cmd_vel", "geometry_msgs/msg/Twist"),
            ("/imu", "sensor_msgs/msg/Imu"),
            ("/joint_states", "sensor_msgs/msg/JointState"),
            ("/odom", "nav_msgs/msg/Odometry"),
            ("/scan", "sensor_msgs/msg/LaserScan"),
            ("/map", "nav_msgs/msg/OccupancyGrid"),
            ("/tf", "tf2_msgs/msg/TFMessage"),
            ("/point_cloud", "sensor_msgs/msg/PointCloud2")
        ]
        for topic, msg_type in topics_with_types:
            subscribe_msg = {
                "op": "subscribe",
                "topic": topic,
                "type": msg_type,
                "throttle_rate": 100
            }
            await self.websocket.send(json.dumps(subscribe_msg))
            logger.info(f"📡 Subscribed to: {topic}")

    async def process_message(self, message):
        """Process ROS2 message and send to both web and Foxglove."""
        try:
            data = json.loads(message)
            if "topic" in data and "msg" in data:
                topic = data["topic"]
                msg = data["msg"]
                # Store latest data for web interface
                if topic in self.latest_data:
                    if topic == "/map":
                        processed_map = self.process_map_data(msg)
                        self.latest_data[topic] = {
                            "timestamp": datetime.now().isoformat(),
                            "data": processed_map
                        }
                    else:
                        self.latest_data[topic] = {
                            "timestamp": datetime.now().isoformat(),
                            "data": msg
                        }
                
                # Send to Foxglove using the correct API for your SDK version
                foxglove_data = {
                    "timestamp": time.time(),
                    "topic": topic,
                    "data": msg
                }
                foxglove.log(topic, foxglove_data)
                
                self.messages_received += 1
                if self.messages_received % 100 == 0:
                    logger.info(f"📊 Received {self.messages_received} messages")
        except json.JSONDecodeError:
            logger.debug("⚠️ Received non-JSON message")
        except Exception as e:
            logger.error(f"❌ Error processing message: {str(e)}")

    def process_map_data(self, map_msg):
        """Process map data efficiently."""
        try:
            info = map_msg.get("info", {})
            map_data = map_msg.get("data", [])
            width = info.get("width", 0)
            height = info.get("height", 0)
            resolution = info.get("resolution", 0.0)
            origin = info.get("origin", {})
            
            # Calculate statistics for visualization
            if isinstance(map_data, list) and len(map_data) > 0:
                occupancy_stats = {
                    "free": sum(1 for cell in map_data if cell == 0),
                    "occupied": sum(1 for cell in map_data if cell == 100),
                    "unknown": sum(1 for cell in map_data if cell == -1),
                    "total": len(map_data)
                }
                occupancy_stats["free_percent"] = (occupancy_stats["free"] / occupancy_stats["total"]) * 100
                occupancy_stats["occupied_percent"] = (occupancy_stats["occupied"] / occupancy_stats["total"]) * 100
                occupancy_stats["unknown_percent"] = (occupancy_stats["unknown"] / occupancy_stats["total"]) * 100
            else:
                occupancy_stats = {
                    "free": 0, "occupied": 0, "unknown": 0, "total": 0,
                    "free_percent": 0, "occupied_percent": 0, "unknown_percent": 0
                }
            
            processed = {
                "info": {
                    "width": width,
                    "height": height,
                    "resolution": resolution,
                    "origin": origin
                },
                "data": map_data,
                "data_length": len(map_data) if isinstance(map_data, list) else 0,
                "has_data": len(map_data) > 0 if isinstance(map_data, list) else False,
                "occupancy_stats": occupancy_stats,
                "summary": f"Map: {width}x{height} pixels, {resolution}m/pixel, {len(map_data)} cells"
            }
            return processed
        except Exception as e:
            logger.error(f"❌ Error processing map data: {str(e)}")
            return {"error": str(e), "has_data": False}

    def get_web_data(self):
        """Get data for web interface."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        return {
            "status": "connected" if self.connected else "disconnected",
            "messages_received": self.messages_received,
            "uptime": uptime,
            "timestamp": datetime.now().isoformat(),
            "data": self.latest_data,
            "foxglove_available": True,
            "foxglove_initialized": self.foxglove_server is not None
        }

class CompatibleWebHandler(SimpleHTTPRequestHandler):
    """Web handler with enhanced static file serving."""
    def __init__(self, *args, data_collector=None, **kwargs):
        self.data_collector = data_collector
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """Handle GET requests efficiently."""
        if self.path == '/':
            self.serve_web_interface()
        elif self.path == '/api/data':
            self.serve_robot_data()
        elif self.path.startswith('/static/'):
            self.serve_static_file()
        else:
            self.send_error(404)

    def serve_web_interface(self):
        """Serve the main web interface with fallback."""
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            html_path = os.path.join(script_dir, 'templates', 'index.html')
            with open(html_path, 'r', encoding='utf-8') as f:
                html = f.read()
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(html.encode())
        except FileNotFoundError:
            html = '''
<!DOCTYPE html>
<html>
<head>
    <title>Compatible ROS2 Visualizer</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 50px; background: #f0f0f0; }
        .info { background: #e3f2fd; padding: 20px; border-radius: 8px; color: #1565c0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .connected { background: #e8f5e9; color: #2e7d32; }
        .disconnected { background: #ffebee; color: #c62828; }
        .feature { margin: 15px 0; padding: 10px; background: #fff; border-left: 4px solid #1565c0; }
    </style>
</head>
<body>
    <div class="info">
        <h1>Compatible ROS2 Visualizer</h1>
        <div class="status connected">
            ✅ Foxglove SDK compatible with your version
        </div>
        
        <div class="feature">
            <h2>📡 Foxglove Connection</h2>
            <p>Connect Foxglove Studio to: <code>ws://localhost:8772</code></p>
            <p>This version uses the correct API for your Foxglove SDK version</p>
        </div>
        
        <div class="feature">
            <h2>ℹ️ Technical Details</h2>
            <p>Your Foxglove SDK uses:</p>
            <ul>
                <li><code>foxglove.start_server()</code> instead of FoxgloveServer class</li>
                <li><code>foxglove.register_channel()</code> for channel registration</li>
                <li><code>foxglove.log()</code> for sending messages</li>
            </ul>
        </div>
    </div>
</body>
</html>
            '''
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(html.encode())

    def serve_static_file(self):
        """Serve static files with proper MIME types."""
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(script_dir, self.path[1:])
            
            # Handle missing files gracefully
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                # Try to serve from default templates if available
                default_path = os.path.join(script_dir, 'templates', 'static', self.path.split('/')[-1])
                if os.path.exists(default_path) and os.path.isfile(default_path):
                    file_path = default_path
                else:
                    self.send_error(404)
                    return
            
            # Determine content type based on extension
            if file_path.endswith('.css'):
                content_type = 'text/css'
            elif file_path.endswith('.js'):
                content_type = 'application/javascript'
            elif file_path.endswith('.png'):
                content_type = 'image/png'
            elif file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif file_path.endswith('.svg'):
                content_type = 'image/svg+xml'
            else:
                content_type = 'text/plain'
            
            with open(file_path, 'rb') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content)
        except Exception as e:
            logger.error(f"❌ Error serving static file {self.path}: {str(e)}")
            self.send_error(500)

    def serve_robot_data(self):
        """Serve robot data as JSON with CORS headers."""
        data = self.data_collector.get_web_data() if self.data_collector else {"error": "No data collector"}
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())

def create_web_handler(data_collector):
    """Create web handler with data collector reference."""
    def handler(*args, **kwargs):
        return CompatibleWebHandler(*args, data_collector=data_collector, **kwargs)
    return handler

async def main():
    """Main function with compatible Foxglove SDK usage."""
    print("\n" + "=" * 60)
    print("🚀 COMPATIBLE ROS2 VISUALIZER - MATCHES YOUR FOXGLOVE SDK VERSION")
    print("=" * 60)
    print("✨ Features:")
    print("  • Uses the correct Foxglove SDK API for your version")
    print("  • Compatible with foxglove.start_server() method")
    print("  • Uses foxglove.register_channel() for channel registration")
    print("  • Uses foxglove.log() for sending messages")
    print("\n🌐 Web Interface: http://localhost:8084")
    print("📡 Foxglove Studio: ws://localhost:8772")
    print("=" * 60 + "\n")
    
    # Create data collector
    data_collector = CompatibleDataCollector()
    
    # Start web server
    web_handler = create_web_handler(data_collector)
    httpd = HTTPServer(('localhost', 8084), web_handler)
    http_thread = threading.Thread(target=httpd.serve_forever)
    http_thread.daemon = True
    http_thread.start()
    logger.info("✅ Web server started on http://localhost:8084")
    
    print("\n⏳ Starting components...")
    
    try:
        # Initialize Foxglove server
        print("📡 Initializing Foxglove server using compatible API...")
        foxglove_initialized = await data_collector.init_foxglove()
        
        if foxglove_initialized:
            print("\n✅ Foxglove server initialized successfully")
            print("   • Connect Foxglove Studio to: ws://localhost:8772")
            print("   • This uses the correct API for your Foxglove SDK version")
        else:
            print("\n⚠️ Foxglove initialization failed but web interface will still work")
        
        print("\n🔗 Connecting to ROS2 bridge...")
        # Start ROS2 connection
        await data_collector.connect_and_listen()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Stopping visualizer...")
        if httpd:
            httpd.shutdown()
            logger.info("✅ Web server stopped")
        print("👋 Visualizer stopped cleanly")
    except Exception as e:
        logger.exception("❌ Critical error in main loop")
        print(f"\n💥 Critical error: {str(e)}")
        print("   Check logs for details")
        if httpd:
            httpd.shutdown()
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Visualizer stopped by user")
    except Exception as e:
        logger.exception("❌ Unexpected error in main")
        print(f"\n💥 Unexpected error: {str(e)}")
        print("   This is likely due to API mismatch with your Foxglove SDK version")
        print("   This version is designed specifically for your SDK version")
        exit(1)