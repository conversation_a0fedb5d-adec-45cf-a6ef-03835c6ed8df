#!/usr/bin/env python3
"""
Map Export Utility for SteriBot
Exports ROS2 occupancy grid maps to PNG/JPEG image files
Supports various color schemes and customization options
"""

import asyncio
import json
import logging
import websockets
import argparse
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional, <PERSON>ple
import numpy as np

# Try to import image processing libraries
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("❌ PIL (Pillow) not available. Install with: pip install Pillow")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MapExporter:
    def __init__(self, ros2_ip: str, ros2_port: int = 8765, timeout: int = 30):
        self.ros2_bridge_url = f"ws://{ros2_ip}:{ros2_port}"
        self.timeout = timeout
        self.map_data = None
        self.export_config = {
            'format': 'png',
            'color_scheme': 'colored',
            'scale_factor': 1.0,
            'jpeg_quality': 90,
            'include_grid': False,
            'include_robot_position': False,
            'colors': {
                'free_space': '#FFFFFF',      # White
                'occupied_space': '#000000',  # Black
                'unknown_space': '#808080',   # Gray
                'robot_position': '#FF0000',  # Red
                'grid_lines': '#CCCCCC',      # Light gray
            }
        }

    async def export_map(self, output_path: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Main method to export map to image file."""
        if not PIL_AVAILABLE:
            return {
                'success': False,
                'error': 'PIL (Pillow) library not available',
                'timestamp': datetime.now().isoformat()
            }

        try:
            # Update configuration
            if config:
                self.export_config.update(config)

            logger.info(f"🗺️ Starting map export to: {output_path}")
            logger.info(f"🔗 ROS2 Bridge: {self.ros2_bridge_url}")

            # Get map data from ROS2
            await self.retrieve_map_data()

            if not self.map_data:
                return {
                    'success': False,
                    'error': 'No map data received from ROS2 bridge',
                    'timestamp': datetime.now().isoformat()
                }

            # Create image from map data
            image = self.create_map_image()

            # Save image
            result = self.save_image(image, output_path)
            
            logger.info(f"✅ Map exported successfully: {output_path}")
            return result

        except Exception as e:
            logger.error(f"❌ Map export failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def retrieve_map_data(self):
        """Retrieve map data from ROS2 bridge."""
        try:
            logger.info(f"🔗 Connecting to ROS2 bridge...")
            
            websocket = await asyncio.wait_for(
                websockets.connect(self.ros2_bridge_url),
                timeout=self.timeout
            )
            
            logger.info("✅ Connected to ROS2 bridge")
            
            # Subscribe to map topic
            subscribe_msg = {
                "op": "subscribe",
                "topic": "/map",
                "type": "nav_msgs/msg/OccupancyGrid",
                "throttle_rate": 1000  # 1 Hz
            }
            await websocket.send(json.dumps(subscribe_msg))
            logger.info("📡 Subscribed to /map topic")
            
            # Listen for map data
            start_time = time.time()
            async for message in websocket:
                if time.time() - start_time > self.timeout:
                    logger.warning("⏰ Timeout waiting for map data")
                    break
                
                try:
                    data = json.loads(message)
                    if "topic" in data and data["topic"] == "/map" and "msg" in data:
                        self.map_data = data["msg"]
                        logger.info("📊 Map data received")
                        break
                except json.JSONDecodeError:
                    continue
            
            await websocket.close()
            
        except Exception as e:
            logger.error(f"❌ Failed to retrieve map data: {e}")
            raise

    def create_map_image(self) -> Image.Image:
        """Create PIL Image from map data."""
        if not self.map_data:
            raise ValueError("No map data available")

        # Extract map information
        info = self.map_data.get("info", {})
        width = info.get("width", 0)
        height = info.get("height", 0)
        resolution = info.get("resolution", 0.05)
        data = self.map_data.get("data", [])

        if width == 0 or height == 0 or not data:
            raise ValueError("Invalid map data")

        logger.info(f"📏 Map dimensions: {width}x{height}, resolution: {resolution}m/pixel")

        # Convert occupancy grid to numpy array
        map_array = np.array(data, dtype=np.int8).reshape((height, width))

        # Apply scale factor
        scale_factor = self.export_config.get('scale_factor', 1.0)
        if scale_factor != 1.0:
            new_height = int(height * scale_factor)
            new_width = int(width * scale_factor)
            # Use nearest neighbor for occupancy grid data
            from PIL import Image as PILImage
            temp_image = PILImage.fromarray(map_array, mode='L')
            temp_image = temp_image.resize((new_width, new_height), PILImage.NEAREST)
            map_array = np.array(temp_image)
            height, width = new_height, new_width

        # Create RGB image
        rgb_image = self.apply_color_scheme(map_array)

        # Convert to PIL Image
        image = Image.fromarray(rgb_image, 'RGB')

        # Add grid if requested
        if self.export_config.get('include_grid', False):
            image = self.add_grid(image)

        # Add robot position if requested and available
        if self.export_config.get('include_robot_position', False):
            image = self.add_robot_position(image)

        return image

    def apply_color_scheme(self, map_array: np.ndarray) -> np.ndarray:
        """Apply color scheme to map array."""
        height, width = map_array.shape
        rgb_image = np.zeros((height, width, 3), dtype=np.uint8)

        colors = self.export_config['colors']
        color_scheme = self.export_config.get('color_scheme', 'colored')

        if color_scheme == 'grayscale':
            # Grayscale mapping
            free_color = (255, 255, 255)      # White
            occupied_color = (0, 0, 0)        # Black
            unknown_color = (128, 128, 128)   # Gray
        elif color_scheme == 'high_contrast':
            # High contrast mapping
            free_color = (255, 255, 255)      # White
            occupied_color = (0, 0, 0)        # Black
            unknown_color = (255, 0, 0)       # Red
        else:  # colored or custom
            free_color = self.hex_to_rgb(colors['free_space'])
            occupied_color = self.hex_to_rgb(colors['occupied_space'])
            unknown_color = self.hex_to_rgb(colors['unknown_space'])

        # Apply colors based on occupancy values
        # -1 = unknown, 0 = free, 100 = occupied
        rgb_image[map_array == 0] = free_color      # Free space
        rgb_image[map_array == 100] = occupied_color # Occupied space
        rgb_image[map_array == -1] = unknown_color   # Unknown space

        # Handle other values (treat as unknown)
        mask = (map_array != 0) & (map_array != 100) & (map_array != -1)
        rgb_image[mask] = unknown_color

        return rgb_image

    def add_grid(self, image: Image.Image) -> Image.Image:
        """Add grid lines to the image."""
        draw = ImageDraw.Draw(image)
        width, height = image.size
        
        grid_spacing = 50  # pixels
        grid_color = self.hex_to_rgb(self.export_config['colors']['grid_lines'])
        
        # Draw vertical lines
        for x in range(0, width, grid_spacing):
            draw.line([(x, 0), (x, height)], fill=grid_color, width=1)
        
        # Draw horizontal lines
        for y in range(0, height, grid_spacing):
            draw.line([(0, y), (width, y)], fill=grid_color, width=1)
        
        return image

    def add_robot_position(self, image: Image.Image) -> Image.Image:
        """Add robot position marker to the image."""
        # This would require robot position data from odometry
        # For now, just add a placeholder marker at center
        draw = ImageDraw.Draw(image)
        width, height = image.size
        center_x, center_y = width // 2, height // 2
        
        robot_color = self.hex_to_rgb(self.export_config['colors']['robot_position'])
        marker_size = 10
        
        # Draw circle marker
        draw.ellipse([
            center_x - marker_size, center_y - marker_size,
            center_x + marker_size, center_y + marker_size
        ], fill=robot_color, outline=robot_color)
        
        return image

    def save_image(self, image: Image.Image, output_path: str) -> Dict[str, Any]:
        """Save image to file."""
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Get format from config or file extension
            format_type = self.export_config.get('format', 'png').upper()
            if format_type == 'JPG':
                format_type = 'JPEG'
            
            # Save with appropriate options
            save_kwargs = {}
            if format_type == 'JPEG':
                save_kwargs['quality'] = self.export_config.get('jpeg_quality', 90)
                save_kwargs['optimize'] = True
                # Convert to RGB if necessary (JPEG doesn't support transparency)
                if image.mode in ('RGBA', 'LA', 'P'):
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
            
            image.save(output_path, format=format_type, **save_kwargs)
            
            # Get file info
            file_size = os.path.getsize(output_path)
            width, height = image.size
            
            return {
                'success': True,
                'filename': os.path.basename(output_path),
                'file_path': output_path,
                'file_size': file_size,
                'dimensions': {'width': width, 'height': height},
                'format': format_type.lower(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to save image: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    @staticmethod
    def hex_to_rgb(hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

async def main():
    parser = argparse.ArgumentParser(description='Export ROS2 map to image file')
    parser.add_argument('--ip', required=True, help='ROS2 bridge IP address')
    parser.add_argument('--port', type=int, default=8765, help='ROS2 bridge port')
    parser.add_argument('--output', required=True, help='Output image file path')
    parser.add_argument('--format', choices=['png', 'jpeg', 'jpg'], default='png', help='Output format')
    parser.add_argument('--color-scheme', choices=['grayscale', 'colored', 'high_contrast'], 
                       default='colored', help='Color scheme')
    parser.add_argument('--scale', type=float, default=1.0, help='Scale factor')
    parser.add_argument('--quality', type=int, default=90, help='JPEG quality (1-100)')
    parser.add_argument('--grid', action='store_true', help='Include grid lines')
    parser.add_argument('--robot-pos', action='store_true', help='Include robot position marker')
    parser.add_argument('--timeout', type=int, default=30, help='Connection timeout in seconds')
    
    args = parser.parse_args()
    
    # Create exporter
    exporter = MapExporter(args.ip, args.port, args.timeout)
    
    # Configure export settings
    config = {
        'format': args.format,
        'color_scheme': args.color_scheme,
        'scale_factor': args.scale,
        'jpeg_quality': args.quality,
        'include_grid': args.grid,
        'include_robot_position': args.robot_pos,
    }
    
    # Export map
    result = await exporter.export_map(args.output, config)
    
    # Output result as JSON for NestJS service
    print(json.dumps(result), flush=True)
    
    if not result['success']:
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
