"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RobotsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RobotsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const robots_service_1 = require("./robots.service");
const create_robot_dto_1 = require("./dto/create-robot.dto");
const update_robot_dto_1 = require("./dto/update-robot.dto");
const get_robot_data_dto_1 = require("./dto/get-robot-data.dto");
const firebase_auth_guard_1 = require("../../common/guards/firebase-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let RobotsController = RobotsController_1 = class RobotsController {
    constructor(robotsService) {
        this.robotsService = robotsService;
        this.logger = new common_1.Logger(RobotsController_1.name);
    }
    create(createRobotDto) {
        return this.robotsService.create(createRobotDto);
    }
    findAll() {
        return this.robotsService.findAll();
    }
    findOne(id) {
        return this.robotsService.findById(id);
    }
    update(id, updateRobotDto) {
        return this.robotsService.update(id, updateRobotDto);
    }
    updateStatus(id, status) {
        return this.robotsService.updateStatus(id, status);
    }
    remove(id) {
        return this.robotsService.remove(id);
    }
    async retrieveRobotData(request) {
        try {
            this.logger.log(`API: Retrieving robot data from ${request.ip_address}:${request.port || 8765}`);
            const result = await this.robotsService.getRobotData(request);
            this.logger.log(`API: Robot data retrieval completed - Status: ${result.connection_status}, ` +
                `Position: ${result.position ? 'Yes' : 'No'}, Battery: ${result.battery_level ? 'Yes' : 'No'}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Robot data retrieval failed', error);
            throw new common_1.HttpException({
                message: 'Failed to retrieve robot data',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async robotDataHealthCheck() {
        try {
            const result = await this.robotsService.robotDataHealthCheck();
            this.logger.log(`API: Robot data health check - Status: ${result.status}`);
            return result;
        }
        catch (error) {
            this.logger.error('API: Robot data health check failed', error);
            throw new common_1.HttpException({
                message: 'Health check failed',
                error: error.message,
                timestamp: new Date().toISOString(),
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.RobotsController = RobotsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Register a new robot (Admin only)" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_robot_dto_1.CreateRobotDto]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all robots" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get robot by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Update robot (Admin only)" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_robot_dto_1.UpdateRobotDto]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(":id/status"),
    (0, swagger_1.ApiOperation)({ summary: "Update robot status" }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete robot (Admin only)' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], RobotsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('data/retrieve'),
    (0, swagger_1.ApiOperation)({
        summary: 'Retrieve real-time robot data from ROS2 bridge',
        description: 'Connects to a specific ROS2 bridge IP and retrieves robot position and battery level using Foxglove SDK',
    }),
    (0, swagger_1.ApiBody)({
        type: get_robot_data_dto_1.GetRobotDataDto,
        description: 'ROS2 bridge connection details',
        examples: {
            example1: {
                summary: 'Basic request',
                value: {
                    ip_address: '*************',
                    port: 9090,
                },
            },
            example2: {
                summary: 'Request with default port',
                value: {
                    ip_address: '*************',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Successfully retrieved robot data',
        type: get_robot_data_dto_1.RobotDataResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data (e.g., invalid IP address)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during data retrieval',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_robot_data_dto_1.GetRobotDataDto]),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "retrieveRobotData", null);
__decorate([
    (0, common_1.Get)('data/health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for robot data service',
        description: 'Returns the health status of the robot data retrieval service',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service health status',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                python_script_exists: { type: 'boolean', example: true },
                timestamp: { type: 'string', example: '2025-07-28T14:30:00.000Z' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RobotsController.prototype, "robotDataHealthCheck", null);
exports.RobotsController = RobotsController = RobotsController_1 = __decorate([
    (0, swagger_1.ApiTags)("Robots"),
    (0, common_1.Controller)("robots"),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [robots_service_1.RobotsService])
], RobotsController);
//# sourceMappingURL=robots.controller.js.map