import { HistoryService } from "./history.service";
import { CreateHistoryDto } from "./dto/create-history.dto";
export declare class HistoryController {
    private readonly historyService;
    constructor(historyService: HistoryService);
    create(createHistoryDto: CreateHistoryDto): Promise<{
        historyId: string;
        timestamp: Date;
        taskId: string;
        userId: string;
        robotId: string;
        zoneId?: string;
        action: string;
        duration?: number;
        result?: string;
        errorMessage?: string;
        batteryUsed?: number;
        disinfectionEfficiency?: number;
    }>;
    findAll(taskId?: string, userId?: string, robotId?: string): Promise<{
        id: string;
    }[]>;
    generateReport(startDate?: string, endDate?: string, userId?: string, robotId?: string, action?: string): Promise<{
        id: string;
    }[]>;
    exportData(startDate?: string, endDate?: string, userId?: string, robotId?: string, action?: string): Promise<{
        data: import("./history.service").HistoryRecord[];
        filename: string;
        contentType: string;
    }>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
