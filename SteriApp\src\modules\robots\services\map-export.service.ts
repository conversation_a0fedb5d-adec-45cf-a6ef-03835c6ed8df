import { Injectable, Logger } from '@nestjs/common';
import { spawn } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { 
  ExportMapImageDto, 
  ExportMapImageResponseDto,
  BatchExportMapDto,
  BatchExportResponseDto,
  MapExportHistoryDto,
  ImageFormat,
  ColorScheme
} from '../dto/map-export.dto';

@Injectable()
export class MapExportService {
  private readonly logger = new Logger(MapExportService.name);
  private readonly pythonScriptPath: string;
  private readonly exportDirectory: string;
  private readonly maxExportHistory = 100;

  constructor() {
    this.pythonScriptPath = path.join(process.cwd(), 'python', 'map_exporter.py');
    this.exportDirectory = path.join(process.cwd(), 'exports', 'maps');
    
    // Ensure export directory exists
    this.ensureExportDirectory();
  }

  private ensureExportDirectory() {
    try {
      if (!fs.existsSync(this.exportDirectory)) {
        fs.mkdirSync(this.exportDirectory, { recursive: true });
        this.logger.log(`📁 Created export directory: ${this.exportDirectory}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create export directory: ${error.message}`);
    }
  }

  async exportMapImage(request: ExportMapImageDto): Promise<ExportMapImageResponseDto> {
    try {
      this.logger.log(`🗺️ Exporting map from ${request.ip_address}:${request.port || 8765}`);

      // Generate filename
      const timestamp = request.add_timestamp !== false ? `_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, -5)}` : '';
      const baseFilename = request.filename || `robot_map_${request.ip_address.replace(/\./g, '_')}`;
      const format = request.format || ImageFormat.PNG;
      const filename = `${baseFilename}${timestamp}.${format}`;
      const outputPath = path.join(this.exportDirectory, filename);

      // Prepare Python script arguments
      const args = [
        this.pythonScriptPath,
        '--ip', request.ip_address,
        '--port', (request.port || 8765).toString(),
        '--output', outputPath,
        '--format', format,
        '--color-scheme', request.color_scheme || ColorScheme.COLORED,
        '--scale', (request.scale_factor || 1.0).toString(),
        '--timeout', '45',
      ];

      if (request.jpeg_quality && format === ImageFormat.JPEG) {
        args.push('--quality', request.jpeg_quality.toString());
      }

      if (request.include_grid) {
        args.push('--grid');
      }

      if (request.include_robot_position) {
        args.push('--robot-pos');
      }

      this.logger.log(`🐍 Executing: python ${args.join(' ')}`);

      // Execute Python script
      const result = await this.executePythonScript(args);

      if (result.success) {
        // Get file stats
        const stats = fs.statSync(outputPath);
        
        const response: ExportMapImageResponseDto = {
          success: true,
          filename: filename,
          file_path: path.relative(process.cwd(), outputPath),
          file_size: stats.size,
          dimensions: result.dimensions || { width: 0, height: 0 },
          map_info: {
            resolution: 0.05, // Default, would be extracted from actual map data
            width: result.dimensions?.width || 0,
            height: result.dimensions?.height || 0,
            origin: { x: 0, y: 0 }, // Would be extracted from actual map data
          },
          exported_at: new Date().toISOString(),
          download_url: `/api/robots/map/download/${filename}`,
        };

        this.logger.log(`✅ Map exported successfully: ${filename} (${stats.size} bytes)`);
        return response;
      } else {
        throw new Error(result.error || 'Unknown export error');
      }
    } catch (error) {
      this.logger.error(`❌ Map export failed: ${error.message}`);
      throw error;
    }
  }

  async batchExportMaps(request: BatchExportMapDto): Promise<BatchExportResponseDto> {
    try {
      this.logger.log(`🗺️ Batch exporting maps from ${request.ip_addresses.length} robots`);

      const results: (ExportMapImageResponseDto & { ip_address: string })[] = [];
      let exportedCount = 0;
      let failedCount = 0;

      // Process each IP address
      for (const ip_address of request.ip_addresses) {
        try {
          const exportRequest: ExportMapImageDto = {
            ip_address,
            port: request.port,
            ...request.export_config,
          };

          const result = await this.exportMapImage(exportRequest);
          results.push({ ...result, ip_address });
          exportedCount++;
        } catch (error) {
          this.logger.error(`Failed to export map from ${ip_address}: ${error.message}`);
          results.push({
            success: false,
            filename: '',
            file_path: '',
            file_size: 0,
            dimensions: { width: 0, height: 0 },
            map_info: { resolution: 0, width: 0, height: 0, origin: { x: 0, y: 0 } },
            exported_at: new Date().toISOString(),
            download_url: '',
            ip_address,
          } as any);
          failedCount++;
        }
      }

      // Create archive if requested
      let archive = undefined;
      if (request.create_archive && exportedCount > 0) {
        archive = await this.createArchive(results.filter(r => r.success));
      }

      const response: BatchExportResponseDto = {
        success: exportedCount > 0,
        exported_count: exportedCount,
        failed_count: failedCount,
        results,
        archive,
        exported_at: new Date().toISOString(),
      };

      this.logger.log(`✅ Batch export completed: ${exportedCount} success, ${failedCount} failed`);
      return response;
    } catch (error) {
      this.logger.error(`❌ Batch export failed: ${error.message}`);
      throw error;
    }
  }

  async getExportHistory(): Promise<MapExportHistoryDto[]> {
    try {
      const files = fs.readdirSync(this.exportDirectory);
      const history: MapExportHistoryDto[] = [];

      for (const filename of files) {
        if (filename.match(/\.(png|jpg|jpeg)$/i)) {
          const filePath = path.join(this.exportDirectory, filename);
          const stats = fs.statSync(filePath);
          
          // Extract IP from filename (basic pattern matching)
          const ipMatch = filename.match(/(\d+_\d+_\d+_\d+)/);
          const ip_address = ipMatch ? ipMatch[1].replace(/_/g, '.') : 'unknown';
          
          const format = filename.toLowerCase().endsWith('.png') ? ImageFormat.PNG : 
                        filename.toLowerCase().endsWith('.jpg') || filename.toLowerCase().endsWith('.jpeg') ? ImageFormat.JPEG : ImageFormat.PNG;

          history.push({
            export_id: `export_${stats.birthtimeMs}`,
            ip_address,
            filename,
            format,
            file_size: stats.size,
            exported_at: stats.birthtime.toISOString(),
            file_exists: true,
            download_url: `/api/robots/map/download/${filename}`,
          });
        }
      }

      // Sort by export date (newest first) and limit
      history.sort((a, b) => new Date(b.exported_at).getTime() - new Date(a.exported_at).getTime());
      return history.slice(0, this.maxExportHistory);
    } catch (error) {
      this.logger.error(`Failed to get export history: ${error.message}`);
      return [];
    }
  }

  async getExportFile(filename: string): Promise<{ filePath: string; exists: boolean }> {
    const filePath = path.join(this.exportDirectory, filename);
    const exists = fs.existsSync(filePath);
    return { filePath, exists };
  }

  async deleteExportFile(filename: string): Promise<boolean> {
    try {
      const filePath = path.join(this.exportDirectory, filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.log(`🗑️ Deleted export file: ${filename}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to delete export file: ${error.message}`);
      return false;
    }
  }

  private async executePythonScript(args: string[]): Promise<any> {
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd(),
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            // Parse JSON output from Python script
            const result = JSON.parse(stdout.trim());
            resolve(result);
          } catch (parseError) {
            reject(new Error(`Failed to parse Python output: ${parseError.message}`));
          }
        } else {
          reject(new Error(`Python script failed with code ${code}: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Failed to start Python process: ${error.message}`));
      });

      // Set timeout
      setTimeout(() => {
        if (!pythonProcess.killed) {
          pythonProcess.kill();
          reject(new Error('Python script timeout'));
        }
      }, 60000); // 60 second timeout
    });
  }

  private async createArchive(results: (ExportMapImageResponseDto & { ip_address: string })[]): Promise<any> {
    // This would implement ZIP archive creation
    // For now, return placeholder
    const archiveFilename = `map_exports_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, -5)}.zip`;
    
    return {
      filename: archiveFilename,
      file_path: `exports/archives/${archiveFilename}`,
      file_size: 0, // Would be calculated
      download_url: `/api/robots/map/download-archive/${archiveFilename}`,
    };
  }

  // Health check
  async healthCheck(): Promise<{ status: string; python_script_exists: boolean; export_directory_exists: boolean }> {
    const pythonScriptExists = fs.existsSync(this.pythonScriptPath);
    const exportDirExists = fs.existsSync(this.exportDirectory);
    
    return {
      status: pythonScriptExists && exportDirExists ? 'healthy' : 'unhealthy',
      python_script_exists: pythonScriptExists,
      export_directory_exists: exportDirExists,
    };
  }
}
