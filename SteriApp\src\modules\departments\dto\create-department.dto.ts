import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateDepartmentDto {
  @ApiProperty({
    description: 'Department name',
    example: 'Engineering',
    minLength: 2
  })
  @IsString({ message: 'Department name must be a string' })
  @MinLength(2, { message: 'Department name must be at least 2 characters long' })
  name: string

  @ApiProperty({
    description: 'Department description',
    example: 'Software development and engineering team',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string

  @ApiProperty({
    description: 'Parent department ID for hierarchical structure',
    example: 'dept_parent_123',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Parent department ID must be a string' })
  parentDepartmentId?: string

  @ApiProperty({
    description: 'Department code or abbreviation',
    example: 'ENG',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Department code must be a string' })
  code?: string
}
