"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirebaseAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let FirebaseAuthGuard = class FirebaseAuthGuard {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new common_1.UnauthorizedException("No token provided");
        }
        try {
            let decodedToken;
            try {
                decodedToken = await this.firebaseService.getAuth().verifyIdToken(token);
            }
            catch (idTokenError) {
                try {
                    const jwt = require('jsonwebtoken');
                    const decoded = jwt.decode(token);
                    if (decoded && decoded.uid) {
                        decodedToken = {
                            uid: decoded.uid,
                            ...decoded.claims,
                        };
                    }
                    else {
                        throw new Error('Invalid token structure');
                    }
                }
                catch (customTokenError) {
                    console.error('Token verification failed:', { idTokenError: idTokenError.message, customTokenError: customTokenError.message });
                    throw new common_1.UnauthorizedException("Invalid token");
                }
            }
            request.user = decodedToken;
            return true;
        }
        catch (error) {
            console.error('Auth guard error:', error);
            throw new common_1.UnauthorizedException("Invalid token");
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(" ") ?? [];
        return type === "Bearer" ? token : undefined;
    }
};
exports.FirebaseAuthGuard = FirebaseAuthGuard;
exports.FirebaseAuthGuard = FirebaseAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], FirebaseAuthGuard);
//# sourceMappingURL=firebase-auth.guard.js.map