import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateUserDto } from "./dto/create-user.dto";
import type { UpdateUserDto } from "./dto/update-user.dto";
import { FileValidationService } from "../../common/validators/file-validation.service";
export declare class UsersService {
    private firebaseService;
    private fileValidationService;
    private readonly collection;
    constructor(firebaseService: FirebaseService, fileValidationService: FileValidationService);
    create(createUserDto: CreateUserDto): Promise<{
        createdAt: Date;
        lastLogin: Date;
        userId: string;
        username: string;
        firstName: string;
        lastName: string;
        email: string;
        role: import("../../common/decorators/roles.decorator").UserRole;
        language?: string;
        jobTitleId?: string;
        departmentId?: string;
        picture?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<any>;
    findByEmail(email: string): Promise<{
        id: string;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<any>;
    updateLastLogin(userId: string): Promise<void>;
    remove(id: string): Promise<{
        message: string;
    }>;
    private populateUserData;
    createProfileResponse(userData: any, jobTitlesService?: any, departmentsService?: any): Promise<{
        userId: any;
        username: any;
        firstName: any;
        lastName: any;
        email: any;
        role: any;
        language: any;
        jobTitle: any;
        department: any;
        picture: any;
        lastLogin: any;
    }>;
    updatePhoto(userId: string, file: any): Promise<{
        message: string;
        photoUrl: string;
        userId: string;
    }>;
}
