{"name": "nestjs-backend", "version": "0.1.0", "private": true, "scripts": {"start": "nest start", "start:dev": "nest start --watch", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "dependencies": {"@fastify/static": "latest", "@nestjs/common": "latest", "@nestjs/config": "latest", "@nestjs/core": "latest", "@nestjs/microservices": "latest", "@nestjs/platform-express": "latest", "@nestjs/swagger": "latest", "@nestjs/websockets": "latest", "@types/jsonwebtoken": "^9.0.10", "@types/ws": "^8.18.1", "class-transformer": "latest", "class-validator": "latest", "firebase": "^11.10.0", "firebase-admin": "latest", "jsonwebtoken": "^9.0.2", "reflect-metadata": "latest", "rxjs": "latest", "ws": "^8.18.3"}, "devDependencies": {"@types/node": "^22", "typescript": "^5"}}