import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateRoomDto } from "./dto/create-room.dto"
import type { UpdateRoomDto } from "./dto/update-room.dto"

@Injectable()
export class RoomsService {
  private readonly collection = "rooms"

  constructor(private firebaseService: FirebaseService) {}

  async create(createRoomDto: CreateRoomDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const roomData = {
      ...createRoomDto,
      roomId: docRef.id,
      createdAt: new Date(),
    }

    await docRef.set(roomData)
    return roomData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByFloor(floorId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where("floorId", "==", floorId).get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Room with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateRoomDto: UpdateRoomDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateRoomDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Room deleted successfully" }
  }
}
