# Firebase Registration Troubleshooting Guide

## The Problem
Passwords are not being registered in Firebase Auth when using the register API, even though the registration appears to succeed.

## Most Likely Causes

### 1. 🔥 Firebase Authentication Not Enabled
**This is the most common cause!**

**Check:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`steribot-23c2c`)
3. Navigate to **Authentication** → **Sign-in method**
4. Ensure **Email/Password** provider is **ENABLED**

**Fix:**
- Click on **Email/Password** provider
- Toggle **Enable** to ON
- Click **Save**

### 2. 🔑 Missing or Incorrect Environment Variables

**Check your `.env` file has all required variables:**
```env
FIREBASE_PROJECT_ID=steribot-23c2c
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_KEY_HERE\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_CLIENT_X509_CERT_URL=your-cert-url
FIREBASE_DATABASE_URL=https://steribot-23c2c-default-rtdb.firebaseio.com/
FIREBASE_WEB_API_KEY=your-web-api-key
```

**Common Issues:**
- Private key format is wrong (missing quotes or `\n` characters)
- Service account email is incorrect
- Web API key is missing

### 3. 🛡️ Service Account Permissions

**Check Firebase Service Account Permissions:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (`steribot-23c2c`)
3. Go to **IAM & Admin** → **IAM**
4. Find your Firebase service account
5. Ensure it has these roles:
   - **Firebase Authentication Admin**
   - **Firebase Admin SDK Administrator Service Agent**

### 4. 🌐 Network/SSL Issues

**Check for network problems:**
- Corporate firewall blocking Firebase
- SSL certificate issues
- Proxy configuration problems

**Fix:**
- Try from a different network
- Check server logs for certificate errors
- Configure proxy if needed

## Debugging Steps

### Step 1: Run the Debug Script
```bash
node debug-firebase-registration.js
```

This will test the complete registration → login flow and identify where it fails.

### Step 2: Check Server Logs
Start your server and watch for these messages:
```bash
npm run start:dev
```

Look for:
- ✅ `Firebase initialized successfully`
- ✅ `Starting user registration for: <EMAIL>`
- ✅ `Firebase user created successfully: uid`
- ❌ Any error messages

### Step 3: Test Firebase Console Directly
1. Go to Firebase Console → Authentication → Users
2. Try to manually add a user with email/password
3. If this fails, the issue is with Firebase project configuration
4. If this works, the issue is with your backend code/configuration

### Step 4: Check Firebase Auth REST API
Test the Firebase Auth REST API directly:
```bash
curl -X POST \
  'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=YOUR_WEB_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "testpass123",
    "returnSecureToken": true
  }'
```

## Quick Fixes to Try

### Fix 1: Enable Email/Password Authentication
1. Firebase Console → Authentication → Sign-in method
2. Enable Email/Password provider
3. Save changes

### Fix 2: Regenerate Service Account Key
1. Firebase Console → Project Settings → Service Accounts
2. Generate new private key
3. Update `.env` file with new credentials

### Fix 3: Add Web API Key
1. Firebase Console → Project Settings → General
2. Scroll to "Your apps" section
3. Copy Web API Key
4. Add to `.env` as `FIREBASE_WEB_API_KEY`

### Fix 4: Check Password Requirements
Firebase requires passwords to be at least 6 characters. Ensure your test passwords meet this requirement.

## Testing the Fix

After making changes:

1. **Restart the server:**
   ```bash
   npm run start:dev
   ```

2. **Run the test script:**
   ```bash
   node test-registration.js
   ```

3. **Check Firebase Console:**
   - Go to Authentication → Users
   - Verify the user was created
   - Try to sign in with the credentials

## Expected Behavior

When working correctly:
1. ✅ Registration API returns success with user ID
2. ✅ User appears in Firebase Console → Authentication → Users
3. ✅ Login API works with the same credentials
4. ✅ Login API fails with wrong password

## Still Having Issues?

If none of the above fixes work:

1. **Check Firebase project status:**
   - Ensure billing is enabled (if required)
   - Verify project is not suspended

2. **Try a fresh Firebase project:**
   - Create a new test project
   - Enable Authentication
   - Test with new credentials

3. **Check Firebase Admin SDK version:**
   ```bash
   npm list firebase-admin
   ```
   - Update if outdated: `npm update firebase-admin`

4. **Enable debug logging:**
   Add to your `.env`:
   ```env
   GOOGLE_APPLICATION_CREDENTIALS_JSON={"your":"service-account-json"}
   ```
