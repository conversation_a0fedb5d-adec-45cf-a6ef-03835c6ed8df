#!/usr/bin/env python3
"""
ROS2 Map Data Retriever
Connects to a specific ROS2 bridge IP and retrieves static map data from /map topic.
Used by the backend API to get the robot's current understanding of its environment.
"""

import asyncio
import json
import logging
import websockets
import time
import sys
from datetime import datetime
from typing import Dict, Optional, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ROS2MapRetriever:
    """Retrieves static map data from ROS2 bridge /map topic."""

    def __init__(self, ip_address: str, port: int = 8765, timeout: float = 15.0):
        self.ip_address = ip_address
        self.port = port
        self.timeout = timeout
        self.websocket_url = f"ws://{ip_address}:{port}"

        # Map data storage
        self.map_data = {
            "info": None,
            "data": None,
            "statistics": None,
            "connection_status": "disconnected",
            "last_updated": None,
            "error_message": None,
            "has_map_data": False,
            "data_length": 0,
            "summary": None
        }

        logger.info(f"ROS2 Map Retriever initialized for {self.websocket_url}")
    
    async def retrieve_map_data(self) -> Dict[str, Any]:
        """Main method to retrieve static map data."""
        logger.info(f"Connecting to ROS2 bridge: {self.websocket_url}")
        
        try:
            # Connect to ROS2 bridge
            websocket = await asyncio.wait_for(
                websockets.connect(self.websocket_url),
                timeout=self.timeout
            )
            
            logger.info("Connected to ROS2 bridge")
            self.map_data["connection_status"] = "connected"
            
            # Subscribe to map topic
            await self.subscribe_to_map_topic(websocket)
            
            # Listen for map data with timeout
            await self.listen_for_map_data(websocket)
            
            await websocket.close()
            
        except asyncio.TimeoutError:
            error_msg = f"Connection timeout to {self.websocket_url}"
            logger.error(f"ERROR: {error_msg}")
            self.map_data["connection_status"] = "timeout"
            self.map_data["error_message"] = error_msg

        except ConnectionRefusedError:
            error_msg = f"Connection refused to {self.websocket_url}"
            logger.error(f"ERROR: {error_msg}")
            self.map_data["connection_status"] = "failed"
            self.map_data["error_message"] = error_msg

        except Exception as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(f"ERROR: {error_msg}")
            self.map_data["connection_status"] = "failed"
            self.map_data["error_message"] = error_msg
        
        # Set last updated timestamp
        self.map_data["last_updated"] = datetime.now().isoformat()
        
        return self.map_data
    
    async def subscribe_to_map_topic(self, websocket):
        """Subscribe to /map topic for occupancy grid data."""
        
        map_subscription = {
            "op": "subscribe",
            "topic": "/map",
            "type": "nav_msgs/msg/OccupancyGrid",
            "throttle_rate": 1000  # 1 Hz - maps don't change frequently
        }
        
        try:
            await websocket.send(json.dumps(map_subscription))
            logger.info("Subscribed to /map topic")
        except Exception as e:
            logger.error(f"Failed to subscribe to /map topic: {e}")
            raise
    
    async def listen_for_map_data(self, websocket):
        """Listen for incoming map data."""
        logger.info("Listening for map data...")

        map_received = False
        start_time = time.time()
        max_wait_time = 12.0  # Wait up to 12 seconds for map data

        try:
            while time.time() - start_time < max_wait_time:
                if map_received:
                    logger.info("Map data received successfully")
                    break
                
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    map_received = await self.process_map_message(message)
                    
                except asyncio.TimeoutError:
                    # Continue listening, this is normal
                    continue
                    
        except Exception as e:
            logger.error(f"Error while listening for map data: {e}")

        if map_received:
            logger.info("Map data retrieval completed successfully")
        else:
            logger.warning("No map data received within timeout period")
    
    async def process_map_message(self, message: str) -> bool:
        """Process incoming ROS2 message and extract map data."""
        try:
            data = json.loads(message)
            
            if "topic" in data and "msg" in data and data["topic"] == "/map":
                msg = data["msg"]
                logger.info("Processing map data from /map topic")
                
                # Extract map information
                info = msg.get("info", {})
                map_data_array = msg.get("data", [])
                
                # Process map info
                self.map_data["info"] = self.process_map_info(info)
                
                # Process map data array
                self.map_data["data"] = map_data_array
                self.map_data["data_length"] = len(map_data_array)
                self.map_data["has_map_data"] = len(map_data_array) > 0
                
                # Calculate statistics
                if map_data_array:
                    self.map_data["statistics"] = self.calculate_map_statistics(map_data_array)
                
                # Generate summary
                if self.map_data["info"]:
                    width = self.map_data["info"]["width"]
                    height = self.map_data["info"]["height"]
                    resolution = self.map_data["info"]["resolution"]
                    self.map_data["summary"] = f"Map: {width}x{height} pixels, {resolution}m/pixel, {len(map_data_array)} cells"
                
                logger.info(f"Map data processed: {self.map_data['summary']}")
                return True
                    
        except json.JSONDecodeError:
            # Ignore non-JSON messages
            pass
        except Exception as e:
            logger.error(f"Error processing map message: {e}")
        
        return False
    
    def process_map_info(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """Process map metadata information."""
        try:
            origin = info.get("origin", {})
            
            processed_info = {
                "width": info.get("width", 0),
                "height": info.get("height", 0),
                "resolution": info.get("resolution", 0.0),
                "origin": {
                    "position": {
                        "x": origin.get("position", {}).get("x", 0.0),
                        "y": origin.get("position", {}).get("y", 0.0),
                        "z": origin.get("position", {}).get("z", 0.0)
                    },
                    "orientation": {
                        "x": origin.get("orientation", {}).get("x", 0.0),
                        "y": origin.get("orientation", {}).get("y", 0.0),
                        "z": origin.get("orientation", {}).get("z", 0.0),
                        "w": origin.get("orientation", {}).get("w", 1.0)
                    }
                },
                "map_load_time": info.get("map_load_time", {}).get("sec", 0)
            }
            
            # Convert timestamp if available
            if processed_info["map_load_time"]:
                processed_info["map_load_time"] = datetime.fromtimestamp(
                    processed_info["map_load_time"]
                ).isoformat()
            else:
                processed_info["map_load_time"] = datetime.now().isoformat()
            
            return processed_info
            
        except Exception as e:
            logger.error(f"Error processing map info: {e}")
            return {}
    
    def calculate_map_statistics(self, map_data: List[int]) -> Dict[str, Any]:
        """Calculate statistics about the map data."""
        try:
            total_cells = len(map_data)
            free_cells = sum(1 for cell in map_data if cell == 0)
            occupied_cells = sum(1 for cell in map_data if cell == 100)
            unknown_cells = sum(1 for cell in map_data if cell == -1)
            
            # Calculate percentages
            free_percentage = (free_cells / total_cells * 100) if total_cells > 0 else 0
            occupied_percentage = (occupied_cells / total_cells * 100) if total_cells > 0 else 0
            unknown_percentage = (unknown_cells / total_cells * 100) if total_cells > 0 else 0
            
            return {
                "total_cells": total_cells,
                "free_cells": free_cells,
                "occupied_cells": occupied_cells,
                "unknown_cells": unknown_cells,
                "free_percentage": round(free_percentage, 1),
                "occupied_percentage": round(occupied_percentage, 1),
                "unknown_percentage": round(unknown_percentage, 1)
            }
            
        except Exception as e:
            logger.error(f"Error calculating map statistics: {e}")
            return {}


async def retrieve_map_data_from_ip(ip_address: str, port: int = 8765) -> Dict[str, Any]:
    """Standalone function to retrieve map data from specific IP."""
    retriever = ROS2MapRetriever(ip_address, port)
    return await retriever.retrieve_map_data()


async def main():
    """Main function for standalone testing."""
    if len(sys.argv) < 2:
        print("Usage: python ros2_map_retriever.py <ip_address> [port]")
        print("Example: python ros2_map_retriever.py ************* 8765")
        sys.exit(1)
    
    ip_address = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8765
    
    print("ROS2 Map Data Retriever")
    print("=" * 50)
    print(f"Target: {ip_address}:{port}")
    print("Retrieving: Static map data from /map topic")
    print()
    
    try:
        result = await retrieve_map_data_from_ip(ip_address, port)
        
        print("MAP RETRIEVAL RESULTS")
        print("=" * 50)
        print(json.dumps(result, indent=2))
        
        return result
        
    except KeyboardInterrupt:
        print("\nMap retrieval interrupted by user")
        return {}
    except Exception as e:
        print(f"\nMap retrieval error: {e}")
        return {}


if __name__ == "__main__":
    asyncio.run(main())
