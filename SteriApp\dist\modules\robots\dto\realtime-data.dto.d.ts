export declare class StartRealtimeStreamDto {
    ip_address: string;
    port?: number;
    include_robot_data?: boolean;
    include_map_data?: boolean;
    update_frequency?: number;
}
export declare class StopRealtimeStreamDto {
    session_id: string;
}
export declare class RealtimeConnectionEventDto {
    event: 'connected' | 'disconnected' | 'connecting' | 'error' | 'reconnecting';
    session_id: string;
    ip_address: string;
    port: number;
    timestamp: string;
    error_message?: string;
}
export declare class RealtimeRobotDataEventDto {
    event: 'robot_data';
    session_id: string;
    position?: {
        x: number;
        y: number;
        z: number;
        orientation: {
            x: number;
            y: number;
            z: number;
            w: number;
        };
        source_topic: string;
    };
    battery_level?: {
        percentage?: number;
        voltage?: number;
        current?: number;
        source_topic: string;
    };
    timestamp: string;
    sequence: number;
}
export declare class RealtimeMapDataEventDto {
    event: 'map_data';
    session_id: string;
    info?: {
        width: number;
        height: number;
        resolution: number;
        origin: {
            position: {
                x: number;
                y: number;
                z: number;
            };
            orientation: {
                x: number;
                y: number;
                z: number;
                w: number;
            };
        };
        map_load_time: string;
    };
    data?: number[];
    statistics?: {
        total_cells: number;
        free_cells: number;
        occupied_cells: number;
        unknown_cells: number;
        free_percentage: number;
        occupied_percentage: number;
        unknown_percentage: number;
    };
    is_full_update: boolean;
    timestamp: string;
    sequence: number;
    data_length: number;
}
export declare class RealtimeStreamStatusDto {
    session_id: string;
    status: 'connected' | 'disconnected' | 'connecting' | 'error';
    ip_address: string;
    port: number;
    include_robot_data: boolean;
    include_map_data: boolean;
    update_frequency: number;
    started_at: string;
    last_data_received?: string;
    messages_received: number;
    connected_clients: number;
}
export type RealtimeEventType = 'connection' | 'robot_data' | 'map_data' | 'stream_status' | 'error';
export type RealtimeEventData = RealtimeConnectionEventDto | RealtimeRobotDataEventDto | RealtimeMapDataEventDto | RealtimeStreamStatusDto;
export interface StreamConfig {
    ip_address: string;
    port: number;
    include_robot_data: boolean;
    include_map_data: boolean;
    update_frequency: number;
    session_id: string;
}
