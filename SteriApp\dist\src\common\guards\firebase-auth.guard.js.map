{"version": 3, "file": "firebase-auth.guard.js", "sourceRoot": "", "sources": ["../../../../src/common/guards/firebase-auth.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA2G;AAC3G,6EAAwE;AAGjE,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAExD,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAA;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAA;QAElD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAqB,CAAC,mBAAmB,CAAC,CAAA;QACtD,CAAC;QAED,IAAI,CAAC;YAEH,IAAI,YAAY,CAAA;YAChB,IAAI,CAAC;gBACH,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAC1E,CAAC;YAAC,OAAO,YAAY,EAAE,CAAC;gBAGtB,IAAI,CAAC;oBACH,MAAM,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;oBACnC,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;oBAEjC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;wBAE3B,YAAY,GAAG;4BACb,GAAG,EAAE,OAAO,CAAC,GAAG;4BAChB,GAAG,OAAO,CAAC,MAAM;yBAElB,CAAA;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;oBAC5C,CAAC;gBACH,CAAC;gBAAC,OAAO,gBAAgB,EAAE,CAAC;oBAC1B,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,YAAY,EAAE,YAAY,CAAC,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC/H,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC;YAED,OAAO,CAAC,IAAI,GAAG,YAAY,CAAA;YAC3B,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;YACzC,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAA;QAClD,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAY;QACzC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QACrE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAA;IAC9C,CAAC;CACF,CAAA;AAnDY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAE0B,kCAAe;GADzC,iBAAiB,CAmD7B"}