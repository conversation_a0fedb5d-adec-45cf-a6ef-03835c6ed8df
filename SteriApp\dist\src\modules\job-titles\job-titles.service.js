"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobTitlesService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
const departments_service_1 = require("../departments/departments.service");
let JobTitlesService = class JobTitlesService {
    constructor(firebaseService, departmentsService) {
        this.firebaseService = firebaseService;
        this.departmentsService = departmentsService;
        this.collection = "jobTitles";
    }
    async create(createJobTitleDto) {
        const firestore = this.firebaseService.getFirestore();
        await this.departmentsService.findById(createJobTitleDto.departmentId);
        const existingJobTitle = await this.findByNameAndDepartment(createJobTitleDto.name, createJobTitleDto.departmentId);
        if (existingJobTitle) {
            throw new common_1.BadRequestException(`Job title '${createJobTitleDto.name}' already exists in this department`);
        }
        const jobTitleData = {
            ...createJobTitleDto,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const docRef = await firestore.collection(this.collection).add(jobTitleData);
        return { id: docRef.id, ...jobTitleData };
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).orderBy('name').get();
        const jobTitles = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
        const populatedJobTitles = await Promise.all(jobTitles.map(async (jobTitle) => {
            try {
                const department = await this.departmentsService.findById(jobTitle.departmentId);
                return {
                    ...jobTitle,
                    department: {
                        id: department.id,
                        name: department.name,
                        description: department.description
                    }
                };
            }
            catch (error) {
                return jobTitle;
            }
        }));
        return populatedJobTitles;
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Job title with ID ${id} not found`);
        }
        const jobTitle = { id: doc.id, ...doc.data() };
        try {
            const department = await this.departmentsService.findById(jobTitle.departmentId);
            jobTitle.department = {
                id: department.id,
                name: department.name,
                description: department.description
            };
        }
        catch (error) {
        }
        return jobTitle;
    }
    async findByNameAndDepartment(name, departmentId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection)
            .where('name', '==', name)
            .where('departmentId', '==', departmentId)
            .get();
        if (snapshot.empty) {
            return null;
        }
        const doc = snapshot.docs[0];
        return { id: doc.id, ...doc.data() };
    }
    async findByDepartment(departmentId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection)
            .where('departmentId', '==', departmentId)
            .orderBy('name')
            .get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async update(id, updateJobTitleDto) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const docRef = firestore.collection(this.collection).doc(id);
            const doc = await docRef.get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`Job title with ID ${id} not found`);
            }
            if (updateJobTitleDto.departmentId) {
                await this.departmentsService.findById(updateJobTitleDto.departmentId);
            }
            if (updateJobTitleDto.name || updateJobTitleDto.departmentId) {
                const currentData = doc.data();
                const nameToCheck = updateJobTitleDto.name || currentData?.name;
                const deptToCheck = updateJobTitleDto.departmentId || currentData?.departmentId;
                const existingJobTitle = await this.findByNameAndDepartment(nameToCheck, deptToCheck);
                if (existingJobTitle && existingJobTitle.id !== id) {
                    throw new common_1.BadRequestException(`Job title '${nameToCheck}' already exists in this department`);
                }
            }
            const updateData = {
                ...updateJobTitleDto,
                updatedAt: new Date(),
            };
            await docRef.update(updateData);
            return this.findById(id);
        }
        catch (error) {
            console.error('Error updating job title:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error(`Failed to update job title: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const doc = await firestore.collection(this.collection).doc(id).get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`Job title with ID ${id} not found`);
            }
            await firestore.collection(this.collection).doc(id).delete();
            return { message: "Job title deleted successfully" };
        }
        catch (error) {
            console.error('Error deleting job title:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error(`Failed to delete job title: ${error.message}`);
        }
    }
};
exports.JobTitlesService = JobTitlesService;
exports.JobTitlesService = JobTitlesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService,
        departments_service_1.DepartmentsService])
], JobTitlesService);
//# sourceMappingURL=job-titles.service.js.map