"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let TasksService = class TasksService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "tasks";
    }
    async create(createTaskDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const taskData = {
            ...createTaskDto,
            taskId: docRef.id,
            status: "pending",
            createdAt: new Date(),
        };
        await docRef.set(taskData);
        return taskData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Task with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async findByUser(userId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where("createdBy", "==", userId).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByRobot(robotId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where("robotId", "==", robotId).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async update(id, updateTaskDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...updateTaskDto,
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async updateStatus(id, status) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        const updateData = {
            status,
            updatedAt: new Date(),
        };
        if (status === "completed") {
            updateData.completedAt = new Date();
        }
        await docRef.update(updateData);
        return this.findById(id);
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "Task deleted successfully" };
    }
};
exports.TasksService = TasksService;
exports.TasksService = TasksService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], TasksService);
//# sourceMappingURL=tasks.service.js.map