"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROS2ConnectionService = void 0;
const common_1 = require("@nestjs/common");
const child_process_1 = require("child_process");
const path = require("path");
const WebSocket = require("ws");
let ROS2ConnectionService = class ROS2ConnectionService {
    constructor() {
        this.scanScriptPath = path.join(process.cwd(), 'Ros_api', 'scan_ros2.py');
    }
    async connectToROS2IPs() {
        try {
            const ips = await this.getIPsFromScan();
            return { ips: await this.testConnections(ips) };
        }
        catch (error) {
            throw new Error(`ROS2 connection failed: ${error.message}`);
        }
    }
    healthCheck() {
        try {
            const fs = require('fs');
            const exists = fs.existsSync(this.scanScriptPath);
            return {
                status: exists ? 'healthy' : 'unhealthy',
                scan_script_exists: exists
            };
        }
        catch {
            return {
                status: 'unhealthy',
                scan_script_exists: false
            };
        }
    }
    async getIPsFromScan() {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                proc.kill();
                reject(new Error('Scan timeout'));
            }, 15000);
            const proc = (0, child_process_1.spawn)('python', [this.scanScriptPath]);
            let output = '';
            proc.stdout.on('data', data => output += data);
            proc.stderr.once('data', data => console.debug(`Scan error: ${data}`));
            proc.on('close', code => {
                clearTimeout(timer);
                if (code !== 0)
                    return reject(new Error(`Scan failed with code ${code}`));
                const ips = output
                    .trim()
                    .split('\n')
                    .map(line => line.match(/ws:\/\/([^:]+):9090/)?.[1])
                    .filter((ip) => !!ip);
                resolve(ips);
            });
            proc.on('error', reject);
        });
    }
    async testConnections(ips) {
        if (ips.length === 0)
            return [];
        const results = [];
        const queue = [...ips];
        while (queue.length > 0) {
            const batch = queue.splice(0, 10);
            const batchResults = await Promise.all(batch.map(ip => this.testConnection(ip)));
            results.push(...batchResults);
        }
        return results;
    }
    testConnection(ip) {
        return new Promise(resolve => {
            const timer = setTimeout(() => {
                resolve({ ip, status: 'failed' });
            }, 3000);
            const ws = new WebSocket(`ws://${ip}:9090`, {
                handshakeTimeout: 2500,
                skipUTF8Validation: true
            });
            ws.on('open', () => {
                clearTimeout(timer);
                ws.close();
                resolve({ ip, status: 'connected' });
            });
            ws.on('error', () => {
                clearTimeout(timer);
                resolve({ ip, status: 'failed' });
            });
        });
    }
};
exports.ROS2ConnectionService = ROS2ConnectionService;
exports.ROS2ConnectionService = ROS2ConnectionService = __decorate([
    (0, common_1.Injectable)()
], ROS2ConnectionService);
//# sourceMappingURL=ros2-connection.service.js.map