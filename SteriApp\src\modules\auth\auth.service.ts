import { Injectable, UnauthorizedException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { UsersService } from "../users/users.service"
import type { RegisterDto } from "./dto/register.dto"
import type { LoginDto } from "./dto/login.dto"
import { UserRole } from "@/common/decorators/roles.decorator"


@Injectable()
export class AuthService {
  constructor(
    private firebaseService: FirebaseService,
    private usersService: UsersService,
  ) {}

  async register(registerDto: RegisterDto) {
    try {
      console.log('Starting user registration for:', registerDto.email)

      // Create user in Firebase Auth
      const userRecord = await this.firebaseService.getAuth().createUser({
        email: registerDto.email,
        password: registerDto.password,
        displayName: registerDto.username,
      })

      console.log('Firebase user created successfully:', userRecord.uid)

      // Create user document in Firestore
      const userData = {
        userId: userRecord.uid,
        username: registerDto.username,
        email: registerDto.email,
        role: registerDto.role || UserRole.USER,
        language: registerDto.language || "en",
        createdAt: new Date().toISOString(),
        lastLogin: null,
      }

      await this.usersService.create(userData)
      console.log('User document created in Firestore')

      return {
        message: "User registered successfully",
        userId: userRecord.uid,
      }
    } catch (error) {
      console.error('Registration error:', error)

      // Provide more specific error messages
      if (error.code === 'auth/email-already-exists') {
        throw new UnauthorizedException('Email already exists')
      } else if (error.code === 'auth/invalid-email') {
        throw new UnauthorizedException('Invalid email address')
      } else if (error.code === 'auth/weak-password') {
        throw new UnauthorizedException('Password is too weak')
      } else if (error.message?.includes('certificate')) {
        throw new UnauthorizedException('Network connection issue. Please check your internet connection or contact your network administrator.')
      }

      throw new UnauthorizedException(`Registration failed: ${error.message}`)
    }
  }

  async validateUser(uid: string) {
    try {
      const userRecord = await this.firebaseService.getAuth().getUser(uid)
      const userData = await this.usersService.findById(uid)

      return {
        ...userRecord,
        ...userData,
      }
    } catch (error) {
      throw new UnauthorizedException("User validation failed")
    }
  }

  async login(loginDto: LoginDto) {
    try {
      console.log('Starting user login for:', loginDto.email)

      // First, verify the email/password combination using Firebase Auth REST API
      await this.verifyEmailPassword(loginDto.email, loginDto.password)

      // If password verification succeeds, get the user data from Firestore
      const userData = await this.usersService.findByEmail(loginDto.email)

      if (!userData) {
        throw new UnauthorizedException('User not found')
      }

      // Create a custom token for the user
      const customToken = await this.firebaseService.getAuth().createCustomToken(userData.id, {
        role: (userData as any).role,
        email: (userData as any).email,
        username: (userData as any).username
      })

      console.log('Login successful for user:', userData.id)

      return {
        message: "Login successful",
        customToken: customToken,
        instructions: "Use this custom token to authenticate with Firebase Auth SDK to get an ID token, or use the test endpoint below",
        user: {
          userId: userData.id,
          email: (userData as any).email,
          username: (userData as any).username,
          role: (userData as any).role
        }
      }
    } catch (error) {
      console.error('Login error:', error)

      // Provide more specific error messages for authentication failures
      if (error.message?.includes('INVALID_PASSWORD') || error.message?.includes('EMAIL_NOT_FOUND')) {
        throw new UnauthorizedException('Invalid email or password')
      } else if (error.message?.includes('TOO_MANY_ATTEMPTS_TRY_LATER')) {
        throw new UnauthorizedException('Too many failed login attempts. Please try again later.')
      } else if (error.message?.includes('USER_DISABLED')) {
        throw new UnauthorizedException('This account has been disabled')
      }

      throw new UnauthorizedException(`Login failed: ${error.message}`)
    }
  }

  private async verifyEmailPassword(email: string, password: string): Promise<void> {
    try {
      // Use Firebase Auth REST API to verify email/password
      const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${process.env.FIREBASE_WEB_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          password: password,
          returnSecureToken: true,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error?.message || 'Authentication failed')
      }

      console.log('Password verification successful for:', email)
    } catch (error) {
      console.error('Password verification failed:', error)
      throw error
    }
  }



  async updateLastLogin(userId: string) {
    await this.usersService.updateLastLogin(userId)
  }
}
