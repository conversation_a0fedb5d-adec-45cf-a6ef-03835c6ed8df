import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateRobotDto } from "./dto/create-robot.dto";
import type { UpdateRobotDto } from "./dto/update-robot.dto";
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto";
import { GetMapDataDto, MapDataResponseDto } from "./dto/get-map-data.dto";
export declare class RobotsService {
    private firebaseService;
    private readonly collection;
    private readonly logger;
    private readonly pythonScriptPath;
    private readonly mapScriptPath;
    constructor(firebaseService: FirebaseService);
    create(createRobotDto: CreateRobotDto): Promise<{
        robotId: string;
        isConnected: boolean;
        createdAt: Date;
        robotName: string;
        serialNumber: string;
        batteryLevel?: number;
        currentPosition?: string;
        ipAddress?: string;
        firmwareVersion?: string;
        maintenanceDate?: string;
        currentTaskId?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRobotDto: UpdateRobotDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: any): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
    getRobotData(request: GetRobotDataDto): Promise<RobotDataResponseDto>;
    private executePythonScript;
    getMapData(request: GetMapDataDto): Promise<MapDataResponseDto>;
    private executeMapScript;
    robotDataHealthCheck(): Promise<{
        status: string;
        python_script_exists: boolean;
        timestamp: string;
    }>;
}
