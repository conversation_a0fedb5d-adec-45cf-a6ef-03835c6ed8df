{"version": 3, "file": "job-titles.service.js", "sourceRoot": "", "sources": ["../../../src/modules/job-titles/job-titles.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmF;AACnF,6EAAwE;AACxE,4EAAuE;AAKhE,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAG3B,YACU,eAAgC,EAChC,kBAAsC;QADtC,oBAAe,GAAf,eAAe,CAAiB;QAChC,uBAAkB,GAAlB,kBAAkB,CAAoB;QAJ/B,eAAU,GAAG,WAAW,CAAA;IAKtC,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,iBAAoC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QAGrD,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAA;QAGtE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACzD,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,YAAY,CAC/B,CAAA;QACD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,4BAAmB,CAC3B,cAAc,iBAAiB,CAAC,IAAI,qCAAqC,CAC1E,CAAA;QACH,CAAC;QAED,MAAM,YAAY,GAAG;YACnB,GAAG,iBAAiB;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAA;QAED,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAC5E,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,YAAY,EAAE,CAAA;IAC3C,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAA;QAClF,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;QAG7E,MAAM,kBAAkB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC1C,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAa,EAAE,EAAE;YACpC,IAAI,CAAC;gBACH,MAAM,UAAU,GAAQ,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;gBACrF,OAAO;oBACL,GAAG,QAAQ;oBACX,UAAU,EAAE;wBACV,EAAE,EAAE,UAAU,CAAC,EAAE;wBACjB,IAAI,EAAE,UAAU,CAAC,IAAI;wBACrB,WAAW,EAAE,UAAU,CAAC,WAAW;qBACpC;iBACF,CAAA;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,OAAO,QAAQ,CAAA;YACjB,CAAC;QACH,CAAC,CAAC,CACH,CAAA;QAED,OAAO,kBAAkB,CAAA;IAC3B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAA;QAErE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAS,CAAA;QAGrD,IAAI,CAAC;YACH,MAAM,UAAU,GAAQ,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;YACrF,QAAQ,CAAC,UAAU,GAAG;gBACpB,EAAE,EAAE,UAAU,CAAC,EAAE;gBACjB,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,WAAW,EAAE,UAAU,CAAC,WAAW;aACpC,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;QAEjB,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,IAAY,EAAE,YAAoB;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aACzD,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;aACzB,KAAK,CAAC,cAAc,EAAE,IAAI,EAAE,YAAY,CAAC;aACzC,GAAG,EAAE,CAAA;QAER,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5B,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAA;IACtC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,YAAoB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;QACrD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aACzD,KAAK,CAAC,cAAc,EAAE,IAAI,EAAE,YAAY,CAAC;aACzC,OAAO,CAAC,MAAM,CAAC;aACf,GAAG,EAAE,CAAA;QAER,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACpE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,iBAAoC;QAC3D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;YACrD,MAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAG5D,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,EAAE,CAAA;YAC9B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAA;YAClE,CAAC;YAGD,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAA;YACxE,CAAC;YAGD,IAAI,iBAAiB,CAAC,IAAI,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC;gBAC7D,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;gBAC9B,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,IAAI,WAAW,EAAE,IAAI,CAAA;gBAC/D,MAAM,WAAW,GAAG,iBAAiB,CAAC,YAAY,IAAI,WAAW,EAAE,YAAY,CAAA;gBAE/E,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;gBACrF,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACnD,MAAM,IAAI,4BAAmB,CAC3B,cAAc,WAAW,qCAAqC,CAC/D,CAAA;gBACH,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG;gBACjB,GAAG,iBAAiB;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAA;YAED,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,IAAI,KAAK,YAAY,0BAAiB,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBAC/E,MAAM,KAAK,CAAA;YACb,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAA;YAGrD,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAA;YACrE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAA;YAClE,CAAC;YAKD,MAAM,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAA;YAC5D,OAAO,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAA;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YACjD,IAAI,KAAK,YAAY,0BAAiB,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBAC/E,MAAM,KAAK,CAAA;YACb,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;CACF,CAAA;AAtLY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAKgB,kCAAe;QACZ,wCAAkB;GALrC,gBAAgB,CAsL5B"}