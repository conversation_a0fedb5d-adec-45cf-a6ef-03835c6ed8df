import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateFloorDto } from "./dto/create-floor.dto";
import type { UpdateFloorDto } from "./dto/update-floor.dto";
export declare class FloorsService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createFloorDto: CreateFloorDto): Promise<{
        floorId: string;
        isActive: boolean;
        createdAt: Date;
        floorNumber: number;
        buildingId: string;
        floorName: string;
        totalRooms: number;
        mapData?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findByBuilding(buildingId: string): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateFloorDto: UpdateFloorDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
