import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsIP } from 'class-validator';

export class GetMapDataDto {
  @ApiProperty({
    description: 'IP address of the ROS2 bridge to connect to',
    example: '*************',
  })
  @IsString()
  @IsIP()
  ip_address: string;

  @ApiProperty({
    description: 'Port of the ROS2 bridge (optional, defaults to 8765)',
    example: 8765,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  port?: number;
}

export class MapOriginDto {
  @ApiProperty({
    description: 'Position of the map origin',
    type: 'object',
    properties: {
      x: { type: 'number', example: -10.0 },
      y: { type: 'number', example: -10.0 },
      z: { type: 'number', example: 0.0 },
    },
  })
  position: {
    x: number;
    y: number;
    z: number;
  };

  @ApiProperty({
    description: 'Orientation of the map origin',
    type: 'object',
    properties: {
      x: { type: 'number', example: 0.0 },
      y: { type: 'number', example: 0.0 },
      z: { type: 'number', example: 0.0 },
      w: { type: 'number', example: 1.0 },
    },
  })
  orientation: {
    x: number;
    y: number;
    z: number;
    w: number;
  };
}

export class MapInfoDto {
  @ApiProperty({
    description: 'Map width in pixels',
    example: 384,
  })
  width: number;

  @ApiProperty({
    description: 'Map height in pixels',
    example: 384,
  })
  height: number;

  @ApiProperty({
    description: 'Map resolution in meters per pixel',
    example: 0.05,
  })
  resolution: number;

  @ApiProperty({
    description: 'Map origin pose',
    type: MapOriginDto,
  })
  origin: MapOriginDto;

  @ApiProperty({
    description: 'Timestamp when the map was created',
    example: '2025-07-30T10:30:00.000Z',
  })
  map_load_time: string;
}

export class MapStatisticsDto {
  @ApiProperty({
    description: 'Total number of cells in the map',
    example: 147456,
  })
  total_cells: number;

  @ApiProperty({
    description: 'Number of free space cells (value = 0)',
    example: 98304,
  })
  free_cells: number;

  @ApiProperty({
    description: 'Number of occupied cells (value = 100)',
    example: 12288,
  })
  occupied_cells: number;

  @ApiProperty({
    description: 'Number of unknown cells (value = -1)',
    example: 36864,
  })
  unknown_cells: number;

  @ApiProperty({
    description: 'Percentage of free space',
    example: 66.7,
  })
  free_percentage: number;

  @ApiProperty({
    description: 'Percentage of occupied space',
    example: 8.3,
  })
  occupied_percentage: number;

  @ApiProperty({
    description: 'Percentage of unknown space',
    example: 25.0,
  })
  unknown_percentage: number;
}

export class MapDataResponseDto {
  @ApiProperty({
    description: 'Map metadata information',
    type: MapInfoDto,
    required: false,
  })
  info?: MapInfoDto;

  @ApiProperty({
    description: 'Occupancy grid data array. Values: -1=unknown, 0=free, 100=occupied',
    type: [Number],
    example: [0, 0, -1, 100, 0, -1],
    required: false,
  })
  data?: number[];

  @ApiProperty({
    description: 'Map statistics and analysis',
    type: MapStatisticsDto,
    required: false,
  })
  statistics?: MapStatisticsDto;

  @ApiProperty({
    description: 'Connection status to the ROS2 bridge',
    enum: ['connected', 'failed', 'timeout', 'disconnected'],
    example: 'connected',
  })
  connection_status: string;

  @ApiProperty({
    description: 'Timestamp when map data was retrieved',
    example: '2025-07-30T10:30:00.000Z',
  })
  last_updated: string;

  @ApiProperty({
    description: 'Error message if retrieval failed',
    example: 'Connection timeout',
    required: false,
  })
  error_message?: string;

  @ApiProperty({
    description: 'IP address that was queried',
    example: '*************',
  })
  queried_ip: string;

  @ApiProperty({
    description: 'Port that was queried',
    example: 8765,
  })
  queried_port: number;

  @ApiProperty({
    description: 'Whether map data was successfully retrieved',
    example: true,
  })
  has_map_data: boolean;

  @ApiProperty({
    description: 'Size of the map data array',
    example: 147456,
  })
  data_length: number;

  @ApiProperty({
    description: 'Summary description of the map',
    example: 'Map: 384x384 pixels, 0.05m/pixel, 147456 cells',
    required: false,
  })
  summary?: string;
}
