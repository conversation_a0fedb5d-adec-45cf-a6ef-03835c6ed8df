import { RobotsService } from "./robots.service";
import { CreateRobotDto } from "./dto/create-robot.dto";
import { UpdateRobotDto } from "./dto/update-robot.dto";
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto";
export declare class RobotsController {
    private readonly robotsService;
    private readonly logger;
    constructor(robotsService: RobotsService);
    create(createRobotDto: CreateRobotDto): Promise<{
        robotId: string;
        isConnected: boolean;
        createdAt: Date;
        robotName: string;
        serialNumber: string;
        batteryLevel?: number;
        currentPosition?: string;
        ipAddress?: string;
        firmwareVersion?: string;
        maintenanceDate?: string;
        currentTaskId?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRobotDto: UpdateRobotDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: any): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
    retrieveRobotData(request: GetRobotDataDto): Promise<RobotDataResponseDto>;
    robotDataHealthCheck(): Promise<{
        status: string;
        python_script_exists: boolean;
        timestamp: string;
    }>;
}
