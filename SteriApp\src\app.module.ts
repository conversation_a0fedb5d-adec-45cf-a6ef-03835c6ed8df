import { Module } from "@nestjs/common"
import { ConfigModule } from "@nestjs/config"
import { AuthModule } from "./modules/auth/auth.module"
import { UsersModule } from "./modules/users/users.module"
import { DepartmentsModule } from "./modules/departments/departments.module"
import { JobTitlesModule } from "./modules/job-titles/job-titles.module"
import { TasksModule } from "./modules/tasks/tasks.module"
import { RobotsModule } from "./modules/robots/robots.module"
import { BuildingsModule } from "./modules/buildings/buildings.module"
import { FloorsModule } from "./modules/floors/floors.module"
import { RoomsModule } from "./modules/rooms/rooms.module"
import { ZonesModule } from "./modules/zones/zones.module"
import { HistoryModule } from "./modules/history/history.module"
import { FirebaseModule } from "./config/firebase/firebase.module"
import { ROS2ConnectionModule } from "./modules/ros2-connection/ros2-connection.module"

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
    }),
    FirebaseModule,
    DepartmentsModule,
    JobTitlesModule,
    AuthModule,
    UsersModule,
    TasksModule,
    RobotsModule,
    BuildingsModule,
    FloorsModule,
    RoomsModule,
    ZonesModule,
    HistoryModule,
    ROS2ConnectionModule,
  ],
})
export class AppModule {}
