import { StreamConfig, RealtimeEventType, RealtimeEventData, RealtimeStreamStatusDto } from '../dto/realtime-data.dto';
export declare class RealtimeDataService {
    private readonly logger;
    private activeStreams;
    private dataEventHandlers;
    private readonly pythonScriptPath;
    constructor();
    onDataReceived(handler: (eventType: RealtimeEventType, data: RealtimeEventData) => void): void;
    private emitEvent;
    startStream(config: StreamConfig): Promise<{
        success: boolean;
        error?: string;
    }>;
    stopStream(sessionId: string): Promise<{
        success: boolean;
        error?: string;
    }>;
    getActiveStreams(): RealtimeStreamStatusDto[];
    private handlePythonOutput;
    private processPythonData;
    private handleConnectionData;
    private handleRobotData;
    private handleMapData;
    private handleStreamError;
    private handleStreamDisconnection;
    private emitConnectionEvent;
    private getConnectedClientsCount;
    cleanup(): Promise<void>;
}
