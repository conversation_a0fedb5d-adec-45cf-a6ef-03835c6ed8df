import { <PERSON>, Get, Post, Patch, Param, Delete, UseGuards, Query, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery } from "@nestjs/swagger"
import { ZonesService } from "./zones.service"
import { CreateZoneDto } from "./dto/create-zone.dto"
import { UpdateZoneDto } from "./dto/update-zone.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Zones")
@Controller("zones")
export class ZonesController {
  constructor(private readonly zonesService: ZonesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new zone (Admin only)" })
  create(@Body() createZoneDto: CreateZoneDto) {
    return this.zonesService.create(createZoneDto)
  }

  @Get()
  @ApiOperation({ summary: 'Get all zones' })
  @ApiQuery({ name: 'roomId', required: false })
  findAll(@Query('roomId') roomId?: string) {
    if (roomId) {
      return this.zonesService.findByRoom(roomId);
    }
    return this.zonesService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get zone by ID' })
  findOne(@Param('id') id: string) {
    return this.zonesService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update zone (Admin only)" })
  update(@Param('id') id: string, @Body() updateZoneDto: UpdateZoneDto) {
    return this.zonesService.update(id, updateZoneDto)
  }

  @Patch(':id/disinfect')
  @ApiOperation({ summary: 'Mark zone as disinfected' })
  markAsDisinfected(@Param('id') id: string) {
    return this.zonesService.markAsDisinfected(id);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete zone (Admin only)' })
  remove(@Param('id') id: string) {
    return this.zonesService.remove(id);
  }
}
