export declare class GetRobotDataDto {
    ip_address: string;
    port?: number;
}
export declare class RobotPositionDto {
    x: number;
    y: number;
    z: number;
    orientation: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    source_topic: string;
}
export declare class RobotBatteryDto {
    percentage?: number;
    voltage?: number;
    current?: number;
    source_topic: string;
}
export declare class RobotDataResponseDto {
    position?: RobotPositionDto;
    battery_level?: RobotBatteryDto;
    connection_status: string;
    last_updated: string;
    error_message?: string;
    queried_ip: string;
    queried_port: number;
    foxglove_enabled: boolean;
}
