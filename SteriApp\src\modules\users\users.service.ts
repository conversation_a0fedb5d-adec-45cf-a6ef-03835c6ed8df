import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateUserDto } from "./dto/create-user.dto"
import type { UpdateUserDto } from "./dto/update-user.dto"

@Injectable()
export class UsersService {
  private readonly collection = "users"

  constructor(private firebaseService: FirebaseService) {}

  async create(createUserDto: CreateUserDto) {
    const firestore = this.firebaseService.getFirestore()

    // Convert date strings to Date objects for storage
    const userData = {
      ...createUserDto,
      createdAt: createUserDto.createdAt ? new Date(createUserDto.createdAt) : new Date(),
      lastLogin: createUserDto.lastLogin ? new Date(createUserDto.lastLogin) : null,
    }

    const docRef = firestore.collection(this.collection).doc(createUserDto.userId)
    await docRef.set(userData)
    return userData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`User with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async findByEmail(email: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where('email', '==', email).get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    try {
      const firestore = this.firebaseService.getFirestore()
      const docRef = firestore.collection(this.collection).doc(id)

      // Check if document exists first
      const doc = await docRef.get()
      if (!doc.exists) {
        throw new NotFoundException(`User with ID ${id} not found`)
      }

      // Convert date strings to Date objects for storage
      const updateData: any = {
        ...updateUserDto,
        updatedAt: new Date(),
      }

      // Convert date strings if they exist
      if (updateUserDto.createdAt) {
        updateData.createdAt = new Date(updateUserDto.createdAt)
      }
      if (updateUserDto.lastLogin) {
        updateData.lastLogin = new Date(updateUserDto.lastLogin)
      }

      await docRef.update(updateData)

      return this.findById(id)
    } catch (error) {
      console.error('Error updating user:', error)
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new Error(`Failed to update user: ${error.message}`)
    }
  }

  async updateLastLogin(userId: string) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(userId)

    await docRef.update({
      lastLogin: new Date(),
    })
  }

  async remove(id: string) {
    try {
      const firestore = this.firebaseService.getFirestore()

      // First, get the user document to find the Firebase Auth UID
      const doc = await firestore.collection(this.collection).doc(id).get()

      if (!doc.exists) {
        throw new NotFoundException(`User with ID ${id} not found`)
      }

      const userData = doc.data()
      const firebaseAuthId = userData?.userId || id // Use userId field or fallback to document ID

      // Delete from Firestore
      await firestore.collection(this.collection).doc(id).delete()

      // Also remove from Firebase Auth if the user exists there
      try {
        await this.firebaseService.getAuth().deleteUser(firebaseAuthId)
      } catch (authError) {
        console.warn(`Could not delete user from Firebase Auth: ${authError.message}`)
        // Continue even if Auth deletion fails, as Firestore deletion succeeded
      }

      return { message: "User deleted successfully" }
    } catch (error) {
      console.error('Error deleting user:', error)
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new Error(`Failed to delete user: ${error.message}`)
    }
  }
}
