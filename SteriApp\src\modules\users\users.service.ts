import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateUserDto } from "./dto/create-user.dto"
import type { UpdateUserDto } from "./dto/update-user.dto"
import { FileValidationService } from "../../common/validators/file-validation.service"

@Injectable()
export class UsersService {
  private readonly collection = "users"

  constructor(
    private firebaseService: FirebaseService,
    private fileValidationService: FileValidationService
  ) {}

  async create(createUserDto: CreateUserDto) {
    const firestore = this.firebaseService.getFirestore()

    // Convert date strings to Date objects for storage
    const userData = {
      ...createUserDto,
      createdAt: createUserDto.createdAt ? new Date(createUserDto.createdAt) : new Date(),
      lastLogin: createUserDto.lastLogin ? new Date(createUserDto.lastLogin) : null,
    }

    const docRef = firestore.collection(this.collection).doc(createUserDto.userId)
    await docRef.set(userData)
    return userData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`User with ID ${id} not found`)
    }

    const userData = { id: doc.id, ...doc.data() }
    return this.populateUserData(userData)
  }

  async findByEmail(email: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where('email', '==', email).get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    try {
      const firestore = this.firebaseService.getFirestore()
      const docRef = firestore.collection(this.collection).doc(id)

      // Check if document exists first
      const doc = await docRef.get()
      if (!doc.exists) {
        throw new NotFoundException(`User with ID ${id} not found`)
      }

      // Convert date strings to Date objects for storage
      const updateData: any = {
        ...updateUserDto,
        updatedAt: new Date(),
      }

      // Convert date strings if they exist
      if (updateUserDto.createdAt) {
        updateData.createdAt = new Date(updateUserDto.createdAt)
      }
      if (updateUserDto.lastLogin) {
        updateData.lastLogin = new Date(updateUserDto.lastLogin)
      }

      await docRef.update(updateData)

      return this.findById(id)
    } catch (error) {
      console.error('Error updating user:', error)
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new Error(`Failed to update user: ${error.message}`)
    }
  }

  async updateLastLogin(userId: string) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(userId)

    await docRef.update({
      lastLogin: new Date(),
    })
  }

  async remove(id: string) {
    try {
      const firestore = this.firebaseService.getFirestore()

      // First, get the user document to find the Firebase Auth UID
      const doc = await firestore.collection(this.collection).doc(id).get()

      if (!doc.exists) {
        throw new NotFoundException(`User with ID ${id} not found`)
      }

      const userData = doc.data()
      const firebaseAuthId = userData?.userId || id // Use userId field or fallback to document ID

      // Delete from Firestore
      await firestore.collection(this.collection).doc(id).delete()

      // Also remove from Firebase Auth if the user exists there
      try {
        await this.firebaseService.getAuth().deleteUser(firebaseAuthId)
      } catch (authError) {
        console.warn(`Could not delete user from Firebase Auth: ${authError.message}`)
        // Continue even if Auth deletion fails, as Firestore deletion succeeded
      }

      return { message: "User deleted successfully" }
    } catch (error) {
      console.error('Error deleting user:', error)
      if (error instanceof NotFoundException) {
        throw error
      }
      throw new Error(`Failed to delete user: ${error.message}`)
    }
  }

  /**
   * Populate user data with job title and department information
   * Note: This is a simplified version that returns the user data as-is
   * Job title and department population will be handled by the auth service
   */
  private async populateUserData(userData: any) {
    return userData
  }

  /**
   * Create optimized profile response with only essential fields
   */
  async createProfileResponse(userData: any, jobTitlesService?: any, departmentsService?: any) {
    const populatedUser = { ...userData }

    // Populate job title information if service is provided
    if (userData.jobTitleId && jobTitlesService) {
      try {
        const jobTitle = await jobTitlesService.findById(userData.jobTitleId)
        populatedUser.jobTitle = {
          id: jobTitle.id,
          name: jobTitle.name,
          description: jobTitle.description
        }
      } catch (error) {
        console.warn(`Job title ${userData.jobTitleId} not found for user ${userData.id}`)
      }
    }

    // Populate department information if service is provided
    if (userData.departmentId && departmentsService) {
      try {
        const department = await departmentsService.findById(userData.departmentId)
        populatedUser.department = {
          id: department.id,
          name: department.name,
          description: department.description
        }
      } catch (error) {
        console.warn(`Department ${userData.departmentId} not found for user ${userData.id}`)
      }
    }

    return {
      userId: populatedUser.id || populatedUser.userId,
      username: populatedUser.username,
      firstName: populatedUser.firstName,
      lastName: populatedUser.lastName,
      email: populatedUser.email,
      role: populatedUser.role,
      language: populatedUser.language || 'en',
      jobTitle: populatedUser.jobTitle || null,
      department: populatedUser.department || null,
      picture: populatedUser.picture || null,
      lastLogin: populatedUser.lastLogin ?
        (populatedUser.lastLogin instanceof Date ?
          populatedUser.lastLogin.toISOString() :
          populatedUser.lastLogin) : null
    }
  }

  /**
   * Update user's profile photo
   * @param userId - User ID
   * @param file - Uploaded image file
   * @returns Promise<{ message: string, photoUrl: string, userId: string }>
   */
  async updatePhoto(userId: string, file: any): Promise<{ message: string, photoUrl: string, userId: string }> {
    try {
      // Validate the uploaded file
      this.fileValidationService.validateImageFile(file)

      // Check if user exists
      const user = await this.findById(userId)
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`)
      }

      // Delete old photo if it exists and is from Firebase Storage
      if (user.picture && user.picture.includes('storage.googleapis.com')) {
        try {
          const oldFilePath = this.firebaseService.extractFilePathFromUrl(user.picture)
          await this.firebaseService.deleteFile(oldFilePath)
        } catch (error) {
          console.warn('Could not delete old profile photo:', error.message)
          // Continue with upload even if old photo deletion fails
        }
      }

      // Generate unique filename
      const fileName = this.fileValidationService.generateUniqueFileName(userId, file.originalname)
      const contentType = this.fileValidationService.getContentTypeFromExtension(fileName)

      // Upload new photo to Firebase Storage
      const photoUrl = await this.firebaseService.uploadFile(
        file.buffer,
        fileName,
        contentType,
        'profile-pictures'
      )

      // Update user document with new photo URL
      const firestore = this.firebaseService.getFirestore()
      await firestore.collection(this.collection).doc(userId).update({
        picture: photoUrl,
        updatedAt: new Date()
      })

      return {
        message: 'Profile photo updated successfully',
        photoUrl,
        userId
      }
    } catch (error) {
      console.error('Error updating user photo:', error)
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new BadRequestException('Failed to update profile photo')
    }
  }
}
