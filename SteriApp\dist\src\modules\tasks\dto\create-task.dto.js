"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTaskDto = exports.TaskPriority = exports.TaskType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var TaskType;
(function (TaskType) {
    TaskType["MANUAL_DISINFECTION"] = "manual_disinfection";
    TaskType["SCHEDULED_DISINFECTION"] = "scheduled_disinfection";
    TaskType["NAVIGATION"] = "navigation";
    TaskType["MAINTENANCE"] = "maintenance";
})(TaskType || (exports.TaskType = TaskType = {}));
var TaskPriority;
(function (TaskPriority) {
    TaskPriority["LOW"] = "low";
    TaskPriority["MEDIUM"] = "medium";
    TaskPriority["HIGH"] = "high";
    TaskPriority["URGENT"] = "urgent";
})(TaskPriority || (exports.TaskPriority = TaskPriority = {}));
class CreateTaskDto {
}
exports.CreateTaskDto = CreateTaskDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the task',
        example: 'Clean Room A'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: TaskType,
        description: 'Type of task to perform',
        example: TaskType.MANUAL_DISINFECTION
    }),
    (0, class_validator_1.IsEnum)(TaskType),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "taskType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: TaskPriority,
        description: 'Priority level of the task',
        example: TaskPriority.HIGH
    }),
    (0, class_validator_1.IsEnum)(TaskPriority),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Scheduled time for the task',
        example: '2024-01-01T10:00:00.000Z'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "scheduledTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the user who created the task',
        example: 'user123'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the robot assigned to the task',
        example: 'robot123'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "robotId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the zone where the task will be performed',
        example: 'zone123'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "zoneId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        description: 'Estimated duration in minutes',
        example: 30
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTaskDto.prototype, "estimatedDuration", void 0);
//# sourceMappingURL=create-task.dto.js.map