import { Controller, Get, Post, Patch, Param, Delete, UseGuards, Body, UploadedFile, UseInterceptors, Request, ForbiddenException } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiConsumes, ApiResponse } from "@nestjs/swagger"
import { FileInterceptor } from "@nestjs/platform-express"
import { UsersService } from "./users.service"
import { CreateUserDto } from "./dto/create-user.dto"
import { UpdateUserDto } from "./dto/update-user.dto"
import { UpdatePhotoDto, UpdatePhotoResponseDto } from "./dto/update-photo.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Users")
@Controller("users")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new user (Admin only)" })
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto)
  }

  @Get()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Get all users (Admin only)" })
  findAll() {
    return this.usersService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  findOne(@Param('id') id: string) {
    return this.usersService.findById(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update user" })
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete user (Admin only)' })
  remove(@Param('id') id: string) {
    return this.usersService.remove(id);
  }

  @Patch(':id/photo')
  @UseInterceptors(FileInterceptor('photo'))
  @ApiOperation({ summary: 'Update user profile photo (User can only update their own photo)' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 200,
    description: 'Profile photo updated successfully',
    type: UpdatePhotoResponseDto
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Can only update own photo' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid file' })
  async updatePhoto(
    @Param('id') userId: string,
    @UploadedFile() file: any,
    @Request() req: any
  ): Promise<UpdatePhotoResponseDto> {
    // Check if user is trying to update their own photo or is an admin
    const currentUserId = req.user?.uid
    const currentUserRole = req.user?.role

    if (currentUserId !== userId && currentUserRole !== UserRole.ADMIN) {
      throw new ForbiddenException('You can only update your own profile photo')
    }

    return this.usersService.updatePhoto(userId, file)
  }
}
