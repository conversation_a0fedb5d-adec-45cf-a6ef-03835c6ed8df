"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROS2ConnectionModule = void 0;
const common_1 = require("@nestjs/common");
const ros2_connection_controller_1 = require("./ros2-connection.controller");
const ros2_connection_service_1 = require("./ros2-connection.service");
let ROS2ConnectionModule = class ROS2ConnectionModule {
};
exports.ROS2ConnectionModule = ROS2ConnectionModule;
exports.ROS2ConnectionModule = ROS2ConnectionModule = __decorate([
    (0, common_1.Module)({
        controllers: [ros2_connection_controller_1.ROS2ConnectionController],
        providers: [ros2_connection_service_1.ROS2ConnectionService],
        exports: [ros2_connection_service_1.ROS2ConnectionService],
    })
], ROS2ConnectionModule);
//# sourceMappingURL=ros2-connection.module.js.map