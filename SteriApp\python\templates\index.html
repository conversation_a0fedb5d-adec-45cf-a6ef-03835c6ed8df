<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SteriBot Simple Web Visualizer</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 SteriBot Simple Web Visualizer</h1>
            <p>Real-time robot data visualization</p>
        </div>
        
        <div class="foxglove-info">
            <strong>📡 Foxglove Studio:</strong> Connect to <code>ws://localhost:8768</code> for advanced visualization<br>
            <strong>🌐 Web Interface:</strong> This page shows the same robot data in real-time
        </div>
        
        <div class="status-bar">
            <div class="status-card">
                <h3>🔗 Connection</h3>
                <div class="status-value" id="connectionStatus">Loading...</div>
            </div>
            <div class="status-card">
                <h3>📊 Messages</h3>
                <div class="status-value" id="messageCount">0</div>
            </div>
            <div class="status-card">
                <h3>⏱️ Uptime</h3>
                <div class="status-value" id="uptime">0s</div>
            </div>
            <div class="status-card">
                <h3>🕒 Last Update</h3>
                <div class="status-value" id="lastUpdate">Never</div>
            </div>
        </div>
        
        <div class="data-grid">
            <div class="data-card">
                <h3>🧭 Robot Odometry (/odom)</h3>
                <div class="timestamp" id="odomTime"></div>
                <div class="data-content" id="odomData">
                    <div class="no-data">Waiting for odometry data...</div>
                </div>
            </div>
            
            <div class="data-card">
                <h3>📡 Laser Scanner (/scan)</h3>
                <div class="timestamp" id="scanTime"></div>
                <div class="data-content" id="scanData">
                    <div class="no-data">Waiting for laser scan data...</div>
                </div>
            </div>
            
            <div class="data-card">
                <h3>🔄 IMU Sensor (/imu)</h3>
                <div class="timestamp" id="imuTime"></div>
                <div class="data-content" id="imuData">
                    <div class="no-data">Waiting for IMU data...</div>
                </div>
            </div>
            
            <div class="data-card">
                <h3>🎮 Velocity Commands (/cmd_vel)</h3>
                <div class="timestamp" id="cmdVelTime"></div>
                <div class="data-content" id="cmdVelData">
                    <div class="no-data">Waiting for velocity commands...</div>
                </div>
            </div>
            
            <div class="data-card">
                <h3>🦾 Joint States (/joint_states)</h3>
                <div class="timestamp" id="jointStatesTime"></div>
                <div class="data-content" id="jointStatesData">
                    <div class="no-data">Waiting for joint states...</div>
                </div>
            </div>
            
            <div class="data-card map-card">
                <h3>🗺️ Robot Map (/map)</h3>
                <div class="map-container">
                    <div class="map-info" id="mapInfo">
                        <div class="no-data">Waiting for map data...</div>
                    </div>
                    <div class="map-canvas-container">
                        <canvas id="mapCanvas" width="600" height="400"></canvas>
                    </div>
                    <div class="map-controls">
                        <button class="map-control-btn" onclick="zoomIn()">🔍 Zoom In</button>
                        <button class="map-control-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                        <button class="map-control-btn" onclick="resetZoom()">🔄 Reset</button>
                        <button class="map-control-btn" onclick="toggleGrid()">📐 Grid</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
