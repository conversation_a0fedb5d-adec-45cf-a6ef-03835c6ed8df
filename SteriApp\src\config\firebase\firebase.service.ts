import { Injectable, type OnModuleInit } from "@nestjs/common"
import { ConfigService } from "@nestjs/config"
import * as admin from "firebase-admin"
import { getFirestore } from "firebase-admin/firestore"
import { getAuth } from "firebase-admin/auth"

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firestore: admin.firestore.Firestore
  private auth: admin.auth.Auth

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      // Set up proxy agent if needed for corporate networks
      if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
        process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
      }

      const serviceAccount = {
        type: "service_account",
        project_id: this.configService.get<string>("FIREBASE_PROJECT_ID"),
        private_key_id: this.configService.get<string>("FIREBASE_PRIVATE_KEY_ID"),
        private_key: this.configService.get<string>("FIREBASE_PRIVATE_KEY")?.replace(/\\n/g, "\n"),
        client_email: this.configService.get<string>("FIREBASE_CLIENT_EMAIL"),
        client_id: this.configService.get<string>("FIREBASE_CLIENT_ID"),
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: this.configService.get<string>("FIREBASE_CLIENT_X509_CERT_URL"),
      }

      if (!admin.apps.length) {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
          databaseURL: this.configService.get<string>("FIREBASE_DATABASE_URL"),
        })
      }

      this.firestore = getFirestore()
      this.auth = getAuth()

      console.log('Firebase initialized successfully')
    } catch (error) {
      console.error('Firebase initialization error:', error)
      throw error
    }
  }

  getFirestore(): admin.firestore.Firestore {
    return this.firestore
  }

  getAuth(): admin.auth.Auth {
    return this.auth
  }
}
