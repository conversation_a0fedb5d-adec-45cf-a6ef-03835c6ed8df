import { UserRole } from "../../../common/decorators/roles.decorator";
export declare class JobTitleDto {
    id: string;
    name: string;
    description?: string;
}
export declare class DepartmentDto {
    id: string;
    name: string;
    description?: string;
}
export declare class UserProfileResponseDto {
    userId: string;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    language: string;
    jobTitle?: JobTitleDto;
    department?: DepartmentDto;
    lastLogin?: string;
    picture?: string;
}
