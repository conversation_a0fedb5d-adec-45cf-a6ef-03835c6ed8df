import { FirebaseService } from "../../config/firebase/firebase.service";
import { UsersService } from "../users/users.service";
import { JobTitlesService } from "../job-titles/job-titles.service";
import { DepartmentsService } from "../departments/departments.service";
import type { RegisterDto } from "./dto/register.dto";
import type { LoginDto } from "./dto/login.dto";
export declare class AuthService {
    private firebaseService;
    private usersService;
    private jobTitlesService;
    private departmentsService;
    constructor(firebaseService: FirebaseService, usersService: UsersService, jobTitlesService: JobTitlesService, departmentsService: DepartmentsService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        userId: string;
    }>;
    validateUser(uid: string): Promise<{
        userId: any;
        username: any;
        firstName: any;
        lastName: any;
        email: any;
        role: any;
        language: any;
        jobTitle: any;
        department: any;
        picture: any;
        lastLogin: any;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        customToken: string;
        instructions: string;
        user: {
            userId: string;
            email: any;
            username: any;
            role: any;
        };
    }>;
    private verifyEmailPassword;
    updateLastLogin(userId: string): Promise<void>;
}
