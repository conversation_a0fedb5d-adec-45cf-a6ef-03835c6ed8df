import { Injectable, NotFoundException, Logger } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { spawn } from 'child_process'
import * as path from 'path'
import type { CreateRobotDto } from "./dto/create-robot.dto"
import type { UpdateRobotDto } from "./dto/update-robot.dto"
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto"

@Injectable()
export class RobotsService {
  private readonly collection = "robots"
  private readonly logger = new Logger(RobotsService.name)
  private readonly pythonScriptPath: string

  constructor(private firebaseService: FirebaseService) {
    // Path to the ROS2 data retriever Python script
    this.pythonScriptPath = path.join(
      process.cwd(),
      'ros_api',
      'ros2_data_retriever.py'
    )
    this.logger.log(`Python script path: ${this.pythonScriptPath}`)
  }

  async create(createRobotDto: CreateRobotDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const robotData = {
      ...createRobotDto,
      robotId: docRef.id,
      isConnected: false,
      createdAt: new Date(),
    }

    await docRef.set(robotData)
    return robotData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Robot with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateRobotDto: UpdateRobotDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateRobotDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async updateStatus(id: string, status: any) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...status,
      lastStatusUpdate: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Robot deleted successfully" }
  }

  /**
   * Retrieve real-time robot data (position and battery) from ROS2 bridge
   */
  async getRobotData(request: GetRobotDataDto): Promise<RobotDataResponseDto> {
    const { ip_address, port = 8765 } = request

    this.logger.log(`Retrieving robot data from ${ip_address}:${port}`)

    try {
      const result = await this.executePythonScript(ip_address, port)

      // Add queried IP and port to response
      const response: RobotDataResponseDto = {
        ...result,
        queried_ip: ip_address,
        queried_port: port,
      }

      this.logger.log(
        `Robot data retrieval completed for ${ip_address}:${port} - Status: ${result.connection_status}`
      )

      return response
    } catch (error) {
      this.logger.error(`Failed to retrieve robot data from ${ip_address}:${port}`, error)

      // Return error response
      return {
        position: undefined,
        battery_level: undefined,
        connection_status: 'failed',
        last_updated: new Date().toISOString(),
        error_message: `Failed to retrieve robot data: ${error.message}`,
        queried_ip: ip_address,
        queried_port: port,
        foxglove_enabled: false,
      }
    }
  }

  /**
   * Execute the Python script to retrieve robot data
   */
  private async executePythonScript(
    ipAddress: string,
    port: number
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const args = [this.pythonScriptPath, ipAddress, port.toString()]

      const pythonProcess = spawn('python', args)
      let stdout = ''
      let stderr = ''

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString()
      })

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          this.logger.error(`Python script exited with code ${code}`)
          this.logger.error(`stderr: ${stderr}`)
          reject(new Error(`Python script failed with code ${code}: ${stderr}`))
          return
        }

        try {
          // Find the JSON result in stdout (after the "RETRIEVAL RESULTS" section)
          const lines = stdout.split('\n')
          let jsonStartIndex = -1

          // Look for the JSON data after "RETRIEVAL RESULTS"
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('RETRIEVAL RESULTS')) {
              jsonStartIndex = i + 2 // Skip the separator line
              break
            }
          }

          if (jsonStartIndex === -1) {
            // If no "RETRIEVAL RESULTS" found, try to parse the entire output
            const result = JSON.parse(stdout)
            resolve(result)
            return
          }

          // Extract JSON from the remaining lines
          const jsonLines = lines.slice(jsonStartIndex)
          const jsonString = jsonLines.join('\n').trim()

          if (jsonString) {
            const result = JSON.parse(jsonString)
            resolve(result)
          } else {
            reject(new Error('No JSON data found in script output'))
          }
        } catch (parseError) {
          this.logger.error('Failed to parse Python script output', parseError)
          this.logger.error(`stdout: ${stdout}`)
          reject(new Error(`Failed to parse script output: ${parseError.message}`))
        }
      })

      pythonProcess.on('error', (error) => {
        this.logger.error('Failed to start Python script', error)
        reject(new Error(`Failed to start Python script: ${error.message}`))
      })

      // Set timeout for the operation (30 seconds)
      setTimeout(() => {
        pythonProcess.kill()
        reject(new Error('Python script execution timeout (30s)'))
      }, 30000)
    })
  }

  /**
   * Health check for the robot data service
   */
  async robotDataHealthCheck(): Promise<{
    status: string
    python_script_exists: boolean
    timestamp: string
  }> {
    const fs = require('fs')
    const scriptExists = fs.existsSync(this.pythonScriptPath)

    return {
      status: scriptExists ? 'healthy' : 'unhealthy',
      python_script_exists: scriptExists,
      timestamp: new Date().toISOString(),
    }
  }
}
