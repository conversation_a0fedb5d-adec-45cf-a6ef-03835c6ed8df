"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloorsService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let FloorsService = class FloorsService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "floors";
    }
    async create(createFloorDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const floorData = {
            ...createFloorDto,
            floorId: docRef.id,
            isActive: true,
            createdAt: new Date(),
        };
        await docRef.set(floorData);
        return floorData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByBuilding(buildingId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where("buildingId", "==", buildingId).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Floor with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateFloorDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...updateFloorDto,
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "Floor deleted successfully" };
    }
};
exports.FloorsService = FloorsService;
exports.FloorsService = FloorsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], FloorsService);
//# sourceMappingURL=floors.service.js.map