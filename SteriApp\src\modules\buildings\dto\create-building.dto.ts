import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateBuildingDto {
  @ApiProperty()
  @IsString()
  buildingName: string

  @ApiProperty()
  @IsString()
  address: string

  @ApiProperty()
  @IsNumber()
  totalFloors: number

  @ApiProperty()
  @IsString()
  managerId: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string
}
