import { Controller, Get, Post, Patch, Param, Delete, UseGuards, Body, HttpException, HttpStatus, Logger } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody, ApiResponse } from "@nestjs/swagger"
import { RobotsService } from "./robots.service"
import { CreateRobotDto } from "./dto/create-robot.dto"
import { UpdateRobotDto } from "./dto/update-robot.dto"
import { GetRobotDataDto, RobotDataResponseDto } from "./dto/get-robot-data.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Robots")
@Controller("robots")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class RobotsController {
  private readonly logger = new Logger(RobotsController.name)

  constructor(private readonly robotsService: RobotsService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Register a new robot (Admin only)" })
  create(@Body() createRobotDto: CreateRobotDto) {
    return this.robotsService.create(createRobotDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all robots" })
  findAll() {
    return this.robotsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get robot by ID' })
  findOne(@Param('id') id: string) {
    return this.robotsService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update robot (Admin only)" })
  update(@Param('id') id: string, @Body() updateRobotDto: UpdateRobotDto) {
    return this.robotsService.update(id, updateRobotDto)
  }

  @Patch(":id/status")
  @ApiOperation({ summary: "Update robot status" })
  updateStatus(@Param('id') id: string, @Body() status: any) {
    return this.robotsService.updateStatus(id, status)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete robot (Admin only)' })
  remove(@Param('id') id: string) {
    return this.robotsService.remove(id);
  }

  @Post('data/retrieve')
  @ApiOperation({
    summary: 'Retrieve real-time robot data from ROS2 bridge',
    description: 'Connects to a specific ROS2 bridge IP and retrieves robot position and battery level using Foxglove SDK',
  })
  @ApiBody({
    type: GetRobotDataDto,
    description: 'ROS2 bridge connection details',
    examples: {
      example1: {
        summary: 'Basic request',
        value: {
          ip_address: '*************',
          port: 9090,
        },
      },
      example2: {
        summary: 'Request with default port',
        value: {
          ip_address: '*************',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Successfully retrieved robot data',
    type: RobotDataResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data (e.g., invalid IP address)',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during data retrieval',
  })
  async retrieveRobotData(@Body() request: GetRobotDataDto): Promise<RobotDataResponseDto> {
    try {
      this.logger.log(`API: Retrieving robot data from ${request.ip_address}:${request.port || 8765}`)

      const result = await this.robotsService.getRobotData(request)

      this.logger.log(
        `API: Robot data retrieval completed - Status: ${result.connection_status}, ` +
        `Position: ${result.position ? 'Yes' : 'No'}, Battery: ${result.battery_level ? 'Yes' : 'No'}`
      )

      return result
    } catch (error) {
      this.logger.error('API: Robot data retrieval failed', error)
      throw new HttpException(
        {
          message: 'Failed to retrieve robot data',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  @Get('data/health')
  @ApiOperation({
    summary: 'Health check for robot data service',
    description: 'Returns the health status of the robot data retrieval service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service health status',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        python_script_exists: { type: 'boolean', example: true },
        timestamp: { type: 'string', example: '2025-07-28T14:30:00.000Z' },
      },
    },
  })
  async robotDataHealthCheck() {
    try {
      const result = await this.robotsService.robotDataHealthCheck()
      this.logger.log(`API: Robot data health check - Status: ${result.status}`)
      return result
    } catch (error) {
      this.logger.error('API: Robot data health check failed', error)
      throw new HttpException(
        {
          message: 'Health check failed',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }
}
