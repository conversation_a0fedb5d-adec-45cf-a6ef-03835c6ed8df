import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateBuildingDto } from "./dto/create-building.dto";
import type { UpdateBuildingDto } from "./dto/update-building.dto";
export declare class BuildingsService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createBuildingDto: CreateBuildingDto): Promise<{
        buildingId: string;
        createdAt: Date;
        buildingName: string;
        address: string;
        totalFloors: number;
        managerId: string;
        description?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateBuildingDto: UpdateBuildingDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
