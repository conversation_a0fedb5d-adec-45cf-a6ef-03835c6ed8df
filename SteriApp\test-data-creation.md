# Test Data Creation Guide

## Prerequisites
- Make sure your NestJS server is running
- You have admin access to create departments and job titles
- Use a tool like <PERSON><PERSON>, <PERSON>, or any HTTP client

## Base URL
```
http://localhost:3000
```

## Step 1: Create Departments

### Create Engineering Department
```bash
POST /departments
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Engineering",
  "description": "Software development and engineering team",
  "code": "ENG"
}
```

### Create Operations Department
```bash
POST /departments
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Operations",
  "description": "Daily operations and maintenance",
  "code": "OPS"
}
```

### Create Quality Assurance Department
```bash
POST /departments
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Quality Assurance",
  "description": "Quality control and testing",
  "code": "QA"
}
```

## Step 2: Get Department IDs

```bash
GET /departments
Authorization: Bearer YOUR_ADMIN_TOKEN
```

Save the department IDs from the response for the next step.

## Step 3: Create Job Titles

### Create Software Engineer Job Title
```bash
POST /job-titles
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Software Engineer",
  "description": "Develops and maintains software applications",
  "departmentId": "ENGINEERING_DEPARTMENT_ID_FROM_STEP_2"
}
```

### Create Operations Manager Job Title
```bash
POST /job-titles
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Operations Manager",
  "description": "Manages daily operations and staff coordination",
  "departmentId": "OPERATIONS_DEPARTMENT_ID_FROM_STEP_2"
}
```

### Create QA Tester Job Title
```bash
POST /job-titles
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "QA Tester",
  "description": "Tests software applications for quality assurance",
  "departmentId": "QA_DEPARTMENT_ID_FROM_STEP_2"
}
```

## Step 4: Get Job Title IDs

```bash
GET /job-titles
Authorization: Bearer YOUR_ADMIN_TOKEN
```

Save the job title IDs from the response for the next step.

## Step 5: Create Test Users

### Create User 1 - Software Engineer
```bash
POST /users
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "userId": "user_001",
  "username": "john_doe",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "user",
  "language": "en",
  "jobTitleId": "SOFTWARE_ENGINEER_JOB_TITLE_ID",
  "departmentId": "ENGINEERING_DEPARTMENT_ID",
  "picture": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
}
```

### Create User 2 - Operations Manager
```bash
POST /users
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "userId": "user_002",
  "username": "jane_smith",
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "role": "admin",
  "language": "en",
  "jobTitleId": "OPERATIONS_MANAGER_JOB_TITLE_ID",
  "departmentId": "OPERATIONS_DEPARTMENT_ID",
  "picture": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
}
```

### Create User 3 - QA Tester
```bash
POST /users
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "userId": "user_003",
  "username": "mike_wilson",
  "firstName": "Mike",
  "lastName": "Wilson",
  "email": "<EMAIL>",
  "role": "user",
  "language": "fr",
  "jobTitleId": "QA_TESTER_JOB_TITLE_ID",
  "departmentId": "QA_DEPARTMENT_ID",
  "picture": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
}
```

## Step 6: Test the Optimized Profile Response

### Test User Profile (as any of the created users)
```bash
GET /auth/profile
Authorization: Bearer USER_TOKEN
```

Expected optimized response:
```json
{
  "userId": "user_001",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "user",
  "language": "en",
  "jobTitle": {
    "id": "jt_123",
    "name": "Software Engineer",
    "description": "Develops and maintains software applications"
  },
  "department": {
    "id": "dept_123",
    "name": "Engineering",
    "description": "Software development and engineering team"
  },
  "picture": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  "lastLogin": "2025-07-30T10:30:00.000Z"
}
```

## Step 7: Test Other Endpoints

### Get All Departments
```bash
GET /departments
Authorization: Bearer YOUR_TOKEN
```

### Get All Job Titles
```bash
GET /job-titles
Authorization: Bearer YOUR_TOKEN
```

### Get Job Titles by Department
```bash
GET /job-titles/department/DEPARTMENT_ID
Authorization: Bearer YOUR_TOKEN
```

### Get All Users (Admin only)
```bash
GET /users
Authorization: Bearer YOUR_ADMIN_TOKEN
```

## User Picture Solutions

You have several options for handling user pictures:

### 1. **External URLs (Current Implementation)**
- Store URLs to external services like Unsplash, Gravatar, or your CDN
- Pros: Simple, no storage needed
- Cons: Dependent on external services

### 2. **Firebase Storage Integration**
```typescript
// Add to users controller
@Post(':id/upload-picture')
@UseInterceptors(FileInterceptor('picture'))
async uploadPicture(
  @Param('id') id: string,
  @UploadedFile() file: Express.Multer.File
) {
  // Upload to Firebase Storage
  // Update user document with new picture URL
}
```

### 3. **Base64 Encoding**
- Store base64 encoded images directly in the database
- Pros: Self-contained
- Cons: Larger database size, slower queries

### 4. **Local File Storage**
```typescript
// Add multer configuration
@Post(':id/upload-picture')
@UseInterceptors(FileInterceptor('picture', {
  storage: diskStorage({
    destination: './uploads/profiles',
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
    }
  })
}))
```

### Recommended: Firebase Storage
For your Firebase-based app, I recommend using Firebase Storage for user pictures. This provides:
- Secure, scalable storage
- Direct integration with your existing Firebase setup
- Automatic CDN distribution
- Built-in image optimization options
