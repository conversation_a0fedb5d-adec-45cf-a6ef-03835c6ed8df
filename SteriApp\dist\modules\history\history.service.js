"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let HistoryService = class HistoryService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "history";
    }
    async create(createHistoryDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const historyData = {
            ...createHistoryDto,
            historyId: docRef.id,
            timestamp: new Date(),
        };
        await docRef.set(historyData);
        return historyData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).orderBy("timestamp", "desc").get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByTask(taskId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore
            .collection(this.collection)
            .where("taskId", "==", taskId)
            .orderBy("timestamp", "desc")
            .get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByUser(userId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore
            .collection(this.collection)
            .where("userId", "==", userId)
            .orderBy("timestamp", "desc")
            .get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByRobot(robotId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore
            .collection(this.collection)
            .where("robotId", "==", robotId)
            .orderBy("timestamp", "desc")
            .get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`History record with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async generateReport(filters) {
        const firestore = this.firebaseService.getFirestore();
        const collectionRef = firestore.collection(this.collection);
        let query = collectionRef;
        if (filters.startDate && filters.endDate) {
            query = query
                .where("timestamp", ">=", new Date(filters.startDate))
                .where("timestamp", "<=", new Date(filters.endDate));
        }
        if (filters.userId) {
            query = query.where("userId", "==", filters.userId);
        }
        if (filters.robotId) {
            query = query.where("robotId", "==", filters.robotId);
        }
        if (filters.action) {
            query = query.where("action", "==", filters.action);
        }
        const snapshot = await query.orderBy("timestamp", "desc").get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async exportData(filters) {
        const data = await this.generateReport(filters);
        const headers = [
            "ID",
            "Task ID",
            "User ID",
            "Robot ID",
            "Zone ID",
            "Action",
            "Timestamp",
            "Duration",
            "Result",
            "Error Message",
            "Battery Used",
            "Disinfection Efficiency",
        ];
        const csvData = await this.generateReport(filters);
        return {
            data: csvData,
            filename: `history_export_${new Date().toISOString().split("T")[0]}.csv`,
            contentType: "text/csv",
        };
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "History record deleted successfully" };
    }
};
exports.HistoryService = HistoryService;
exports.HistoryService = HistoryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], HistoryService);
//# sourceMappingURL=history.service.js.map