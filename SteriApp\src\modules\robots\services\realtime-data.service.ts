import { Injectable, Logger } from '@nestjs/common';
import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import { 
  StreamConfig, 
  RealtimeEventType, 
  RealtimeEventData,
  RealtimeStreamStatusDto,
  RealtimeConnectionEventDto,
  RealtimeRobotDataEventDto,
  RealtimeMapDataEventDto
} from '../dto/realtime-data.dto';

interface ActiveStream {
  config: StreamConfig;
  process: ChildProcess;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  started_at: string;
  last_data_received?: string;
  messages_received: number;
  error_message?: string;
}

@Injectable()
export class RealtimeDataService {
  private readonly logger = new Logger(RealtimeDataService.name);
  private activeStreams = new Map<string, ActiveStream>();
  private dataEventHandlers: ((eventType: RealtimeEventType, data: RealtimeEventData) => void)[] = [];
  private readonly pythonScriptPath: string;

  constructor() {
    this.pythonScriptPath = path.join(process.cwd(), 'Ros_api', 'realtime_robot_bridge.py');
  }

  // Event handler registration
  onDataReceived(handler: (eventType: RealtimeEventType, data: RealtimeEventData) => void) {
    this.dataEventHandlers.push(handler);
  }

  private emitEvent(eventType: RealtimeEventType, data: RealtimeEventData) {
    this.dataEventHandlers.forEach(handler => {
      try {
        handler(eventType, data);
      } catch (error) {
        this.logger.error(`Error in event handler: ${error.message}`);
      }
    });
  }

  async startStream(config: StreamConfig): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if stream already exists
      if (this.activeStreams.has(config.session_id)) {
        return { success: false, error: 'Stream already exists' };
      }

      this.logger.log(`🚀 Starting realtime stream: ${config.session_id}`);

      // Create the stream entry
      const stream: ActiveStream = {
        config,
        process: null,
        status: 'connecting',
        started_at: new Date().toISOString(),
        messages_received: 0,
      };

      this.activeStreams.set(config.session_id, stream);

      // Start Python process
      const pythonArgs = [
        this.pythonScriptPath,
        '--ip', config.ip_address,
        '--port', config.port.toString(),
        '--session-id', config.session_id,
        '--update-frequency', config.update_frequency.toString(),
      ];

      if (config.include_robot_data) {
        pythonArgs.push('--include-robot-data');
      }

      if (config.include_map_data) {
        pythonArgs.push('--include-map-data');
      }

      this.logger.log(`🐍 Starting Python process: python ${pythonArgs.join(' ')}`);

      const pythonProcess = spawn('python', pythonArgs, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd(),
      });

      stream.process = pythonProcess;

      // Handle process output
      pythonProcess.stdout.on('data', (data) => {
        this.handlePythonOutput(config.session_id, data.toString());
      });

      pythonProcess.stderr.on('data', (data) => {
        this.logger.error(`Python stderr [${config.session_id}]: ${data.toString()}`);
      });

      pythonProcess.on('close', (code) => {
        this.logger.log(`Python process closed [${config.session_id}]: ${code}`);
        this.handleStreamDisconnection(config.session_id, `Process exited with code ${code}`);
      });

      pythonProcess.on('error', (error) => {
        this.logger.error(`Python process error [${config.session_id}]: ${error.message}`);
        this.handleStreamError(config.session_id, error.message);
      });

      // Emit connection event
      this.emitConnectionEvent(config.session_id, 'connecting');

      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to start stream: ${error.message}`);
      this.activeStreams.delete(config.session_id);
      return { success: false, error: error.message };
    }
  }

  async stopStream(sessionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const stream = this.activeStreams.get(sessionId);
      if (!stream) {
        return { success: false, error: 'Stream not found' };
      }

      this.logger.log(`🛑 Stopping stream: ${sessionId}`);

      // Kill the Python process
      if (stream.process && !stream.process.killed) {
        stream.process.kill('SIGTERM');
        
        // Force kill after 5 seconds if still running
        setTimeout(() => {
          if (stream.process && !stream.process.killed) {
            stream.process.kill('SIGKILL');
          }
        }, 5000);
      }

      // Remove from active streams
      this.activeStreams.delete(sessionId);

      // Emit disconnection event
      this.emitConnectionEvent(sessionId, 'disconnected');

      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to stop stream: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  getActiveStreams(): RealtimeStreamStatusDto[] {
    const streams: RealtimeStreamStatusDto[] = [];
    
    for (const [sessionId, stream] of this.activeStreams) {
      streams.push({
        session_id: sessionId,
        status: stream.status,
        ip_address: stream.config.ip_address,
        port: stream.config.port,
        include_robot_data: stream.config.include_robot_data,
        include_map_data: stream.config.include_map_data,
        update_frequency: stream.config.update_frequency,
        started_at: stream.started_at,
        last_data_received: stream.last_data_received,
        messages_received: stream.messages_received,
        connected_clients: this.getConnectedClientsCount(),
      });
    }

    return streams;
  }

  private handlePythonOutput(sessionId: string, output: string) {
    try {
      const lines = output.trim().split('\n');
      
      for (const line of lines) {
        if (!line.trim()) continue;

        try {
          const data = JSON.parse(line);
          this.processPythonData(sessionId, data);
        } catch (parseError) {
          // Not JSON, might be log message
          this.logger.debug(`Python log [${sessionId}]: ${line}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error handling Python output: ${error.message}`);
    }
  }

  private processPythonData(sessionId: string, data: any) {
    const stream = this.activeStreams.get(sessionId);
    if (!stream) return;

    // Update stream stats
    stream.messages_received++;
    stream.last_data_received = new Date().toISOString();

    // Process different data types
    switch (data.type) {
      case 'connection':
        this.handleConnectionData(sessionId, data);
        break;
      case 'robot_data':
        this.handleRobotData(sessionId, data);
        break;
      case 'map_data':
        this.handleMapData(sessionId, data);
        break;
      case 'error':
        this.handleStreamError(sessionId, data.message);
        break;
      default:
        this.logger.debug(`Unknown data type from Python: ${data.type}`);
    }
  }

  private handleConnectionData(sessionId: string, data: any) {
    const stream = this.activeStreams.get(sessionId);
    if (!stream) return;

    if (data.status === 'connected') {
      stream.status = 'connected';
      this.emitConnectionEvent(sessionId, 'connected');
    } else if (data.status === 'disconnected') {
      this.handleStreamDisconnection(sessionId, data.message);
    }
  }

  private handleRobotData(sessionId: string, data: any) {
    const robotDataEvent: RealtimeRobotDataEventDto = {
      event: 'robot_data',
      session_id: sessionId,
      position: data.position,
      battery_level: data.battery_level,
      timestamp: data.timestamp || new Date().toISOString(),
      sequence: data.sequence || 0,
    };

    this.emitEvent('robot_data', robotDataEvent);
  }

  private handleMapData(sessionId: string, data: any) {
    const mapDataEvent: RealtimeMapDataEventDto = {
      event: 'map_data',
      session_id: sessionId,
      info: data.info,
      data: data.data,
      statistics: data.statistics,
      is_full_update: data.is_full_update || true,
      timestamp: data.timestamp || new Date().toISOString(),
      sequence: data.sequence || 0,
      data_length: data.data ? data.data.length : 0,
    };

    this.emitEvent('map_data', mapDataEvent);
  }

  private handleStreamError(sessionId: string, errorMessage: string) {
    const stream = this.activeStreams.get(sessionId);
    if (stream) {
      stream.status = 'error';
      stream.error_message = errorMessage;
    }

    this.emitConnectionEvent(sessionId, 'error', errorMessage);
  }

  private handleStreamDisconnection(sessionId: string, reason?: string) {
    const stream = this.activeStreams.get(sessionId);
    if (stream) {
      stream.status = 'disconnected';
    }

    this.emitConnectionEvent(sessionId, 'disconnected', reason);
  }

  private emitConnectionEvent(sessionId: string, event: 'connected' | 'disconnected' | 'connecting' | 'error' | 'reconnecting', errorMessage?: string) {
    const stream = this.activeStreams.get(sessionId);
    if (!stream) return;

    const connectionEvent: RealtimeConnectionEventDto = {
      event,
      session_id: sessionId,
      ip_address: stream.config.ip_address,
      port: stream.config.port,
      timestamp: new Date().toISOString(),
      error_message: errorMessage,
    };

    this.emitEvent('connection', connectionEvent);
  }

  private getConnectedClientsCount(): number {
    // This will be set by the gateway
    return 0;
  }

  // Cleanup method
  async cleanup() {
    this.logger.log('🧹 Cleaning up realtime data service...');
    
    const stopPromises = Array.from(this.activeStreams.keys()).map(sessionId => 
      this.stopStream(sessionId)
    );

    await Promise.all(stopPromises);
    this.logger.log('✅ Cleanup completed');
  }
}
