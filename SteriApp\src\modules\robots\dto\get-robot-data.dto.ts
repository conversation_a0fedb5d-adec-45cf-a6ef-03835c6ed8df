import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsIP } from 'class-validator';

export class GetRobotDataDto {
  @ApiProperty({
    description: 'IP address of the ROS2 bridge to connect to',
    example: '*************',
  })
  @IsString()
  @IsIP()
  ip_address: string;

  @ApiProperty({
    description: 'Port of the ROS2 bridge (optional, defaults to 8765)',
    example: 8765,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  port?: number;
}

export class RobotPositionDto {
  @ApiProperty({
    description: 'X coordinate of robot position',
    example: 1.25,
  })
  x: number;

  @ApiProperty({
    description: 'Y coordinate of robot position', 
    example: -0.75,
  })
  y: number;

  @ApiProperty({
    description: 'Z coordinate of robot position',
    example: 0.0,
  })
  z: number;

  @ApiProperty({
    description: 'Robot orientation quaternion',
    type: 'object',
    properties: {
      x: { type: 'number', example: 0.0 },
      y: { type: 'number', example: 0.0 },
      z: { type: 'number', example: 0.707 },
      w: { type: 'number', example: 0.707 },
    },
  })
  orientation: {
    x: number;
    y: number;
    z: number;
    w: number;
  };

  @ApiProperty({
    description: 'Source ROS2 topic for position data',
    example: '/odom',
  })
  source_topic: string;
}

export class RobotBatteryDto {
  @ApiProperty({
    description: 'Battery percentage (0-100)',
    example: 85.5,
    required: false,
  })
  percentage?: number;

  @ApiProperty({
    description: 'Battery voltage in volts',
    example: 12.6,
    required: false,
  })
  voltage?: number;

  @ApiProperty({
    description: 'Battery current in amperes',
    example: -2.1,
    required: false,
  })
  current?: number;

  @ApiProperty({
    description: 'Source ROS2 topic for battery data',
    example: '/battery_state',
  })
  source_topic: string;
}

export class RobotDataResponseDto {
  @ApiProperty({
    description: 'Current position of the robot',
    type: RobotPositionDto,
    required: false,
  })
  position?: RobotPositionDto;

  @ApiProperty({
    description: 'Current battery level of the robot',
    type: RobotBatteryDto,
    required: false,
  })
  battery_level?: RobotBatteryDto;

  @ApiProperty({
    description: 'Connection status to the ROS2 bridge',
    enum: ['connected', 'failed', 'timeout', 'disconnected'],
    example: 'connected',
  })
  connection_status: string;

  @ApiProperty({
    description: 'Timestamp when data was last updated',
    example: '2025-07-28T14:30:00.000Z',
  })
  last_updated: string;

  @ApiProperty({
    description: 'Error message if connection failed',
    example: 'Connection timeout',
    required: false,
  })
  error_message?: string;

  @ApiProperty({
    description: 'IP address that was queried',
    example: '*************',
  })
  queried_ip: string;

  @ApiProperty({
    description: 'Port that was queried',
    example: 8765,
  })
  queried_port: number;

  @ApiProperty({
    description: 'Whether Foxglove SDK is enabled for data logging',
    example: true,
  })
  foxglove_enabled: boolean;
}
