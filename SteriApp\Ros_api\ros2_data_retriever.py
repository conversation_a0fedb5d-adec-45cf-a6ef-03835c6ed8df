#!/usr/bin/env python3
"""
ROS2 Data Retriever
Connects to a specific ROS2 bridge IP and retrieves robot position and battery data.
Used by the backend API to get specific robot information.
"""

import asyncio
import json
import logging
import websockets
import time
import sys
from datetime import datetime
from typing import Dict, Optional, Any

try:
    import foxglove
    from foxglove import Channel
    FOXGLOVE_AVAILABLE = True
    print("Foxglove SDK available")
except ImportError:
    FOXGLOVE_AVAILABLE = False
    print("Foxglove SDK not available. Install with: pip install foxglove-sdk")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ROS2DataRetriever:
    """Retrieves specific data from ROS2 bridge: position and battery level."""

    def __init__(self, ip_address: str, port: int = 8765, timeout: float = 10.0):
        self.ip_address = ip_address
        self.port = port
        self.timeout = timeout
        self.websocket_url = f"ws://{ip_address}:{port}"

        # Data storage
        self.robot_data = {
            "position": None,
            "battery_level": None,
            "connection_status": "disconnected",
            "last_updated": None,
            "error_message": None,
            "foxglove_enabled": FOXGLOVE_AVAILABLE
        }

        # Initialize Foxglove if available
        if FOXGLOVE_AVAILABLE:
            try:
                foxglove.set_log_level(logging.INFO)
                logger.info("Foxglove SDK initialized for data logging")
            except Exception as e:
                logger.warning(f"Failed to initialize Foxglove SDK: {e}")

        logger.info(f"ROS2 Data Retriever initialized for {self.websocket_url}")
    
    async def retrieve_robot_data(self) -> Dict[str, Any]:
        """Main method to retrieve robot position and battery data."""
        logger.info(f"Connecting to ROS2 bridge: {self.websocket_url}")
        
        try:
            # Connect to ROS2 bridge
            websocket = await asyncio.wait_for(
                websockets.connect(self.websocket_url),
                timeout=self.timeout
            )
            
            logger.info("Connected to ROS2 bridge")
            self.robot_data["connection_status"] = "connected"
            
            # Subscribe to relevant topics
            await self.subscribe_to_topics(websocket)
            
            # Listen for data with timeout
            await self.listen_for_data(websocket)
            
            await websocket.close()
            
        except asyncio.TimeoutError:
            error_msg = f"Connection timeout to {self.websocket_url}"
            logger.error(f"ERROR: {error_msg}")
            self.robot_data["connection_status"] = "timeout"
            self.robot_data["error_message"] = error_msg

        except ConnectionRefusedError:
            error_msg = f"Connection refused to {self.websocket_url}"
            logger.error(f"ERROR: {error_msg}")
            self.robot_data["connection_status"] = "failed"
            self.robot_data["error_message"] = error_msg

        except Exception as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(f"ERROR: {error_msg}")
            self.robot_data["connection_status"] = "failed"
            self.robot_data["error_message"] = error_msg
        
        # Set last updated timestamp
        self.robot_data["last_updated"] = datetime.now().isoformat()
        
        return self.robot_data
    
    async def subscribe_to_topics(self, websocket):
        """Subscribe to topics that contain position and battery data."""
        
        # Topics to subscribe to for robot data
        topics_to_subscribe = [
            # Position data - typically from odometry
            {
                "op": "subscribe",
                "topic": "/odom",
                "type": "nav_msgs/msg/Odometry",
                "throttle_rate": 500  # 2 Hz
            },
            # Alternative position topics
            {
                "op": "subscribe", 
                "topic": "/robot_pose",
                "type": "geometry_msgs/msg/PoseStamped",
                "throttle_rate": 500
            },
            # Battery data - common battery topics
            {
                "op": "subscribe",
                "topic": "/battery_state",
                "type": "sensor_msgs/msg/BatteryState", 
                "throttle_rate": 1000  # 1 Hz
            },
            {
                "op": "subscribe",
                "topic": "/battery",
                "type": "sensor_msgs/msg/BatteryState",
                "throttle_rate": 1000
            },
            # Alternative battery topics
            {
                "op": "subscribe",
                "topic": "/power_status",
                "type": "std_msgs/msg/Float32",
                "throttle_rate": 1000
            }
        ]
        
        for subscription in topics_to_subscribe:
            try:
                await websocket.send(json.dumps(subscription))
                logger.info(f"Subscribed to: {subscription['topic']}")
            except Exception as e:
                logger.warning(f"Failed to subscribe to {subscription['topic']}: {e}")
    
    async def listen_for_data(self, websocket):
        """Listen for incoming data and extract position/battery information."""
        logger.info("Listening for robot data...")

        data_received = {"position": False, "battery": False}
        start_time = time.time()
        max_wait_time = 8.0  # Wait up to 8 seconds for data

        try:
            while time.time() - start_time < max_wait_time:
                # Check if we have both types of data or timeout
                if data_received["position"] and data_received["battery"]:
                    logger.info("Received both position and battery data")
                    break
                
                try:
                    # Wait for message with short timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    await self.process_message(message, data_received)
                    
                except asyncio.TimeoutError:
                    # Continue listening, this is normal
                    continue
                    
        except Exception as e:
            logger.error(f"Error while listening for data: {e}")

        # Log what data we received
        if data_received["position"]:
            logger.info("Position data retrieved")
        else:
            logger.warning("No position data received")

        if data_received["battery"]:
            logger.info("Battery data retrieved")
        else:
            logger.warning("No battery data received")
    
    async def process_message(self, message: str, data_received: Dict[str, bool]):
        """Process incoming ROS2 message and extract relevant data."""
        try:
            data = json.loads(message)
            
            if "topic" in data and "msg" in data:
                topic = data["topic"]
                msg = data["msg"]

                # Log to Foxglove if available
                if FOXGLOVE_AVAILABLE:
                    try:
                        foxglove_data = {
                            "timestamp": time.time(),
                            "topic": topic,
                            "data": msg,
                            "source": "ros2_data_retriever",
                            "robot_ip": self.ip_address
                        }
                        foxglove.log(f"robot_data_{topic.replace('/', '_')}", foxglove_data)
                    except Exception as e:
                        logger.debug(f"Foxglove logging failed for {topic}: {e}")

                # Process position data
                if topic in ["/odom", "/robot_pose"]:
                    self.extract_position_data(topic, msg)
                    data_received["position"] = True

                # Process battery data
                elif topic in ["/battery_state", "/battery", "/power_status"]:
                    self.extract_battery_data(topic, msg)
                    data_received["battery"] = True
                    
        except json.JSONDecodeError:
            # Ignore non-JSON messages
            pass
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def extract_position_data(self, topic: str, msg: Dict[str, Any]):
        """Extract robot position from odometry or pose messages."""
        try:
            if topic == "/odom":
                # Extract from odometry message
                pose = msg.get("pose", {}).get("pose", {})
                position = pose.get("position", {})
                orientation = pose.get("orientation", {})
                
                self.robot_data["position"] = {
                    "x": position.get("x", 0.0),
                    "y": position.get("y", 0.0), 
                    "z": position.get("z", 0.0),
                    "orientation": {
                        "x": orientation.get("x", 0.0),
                        "y": orientation.get("y", 0.0),
                        "z": orientation.get("z", 0.0),
                        "w": orientation.get("w", 1.0)
                    },
                    "source_topic": topic
                }
                
            elif topic == "/robot_pose":
                # Extract from pose stamped message
                pose = msg.get("pose", {})
                position = pose.get("position", {})
                orientation = pose.get("orientation", {})
                
                self.robot_data["position"] = {
                    "x": position.get("x", 0.0),
                    "y": position.get("y", 0.0),
                    "z": position.get("z", 0.0), 
                    "orientation": {
                        "x": orientation.get("x", 0.0),
                        "y": orientation.get("y", 0.0),
                        "z": orientation.get("z", 0.0),
                        "w": orientation.get("w", 1.0)
                    },
                    "source_topic": topic
                }
            
            logger.info(f"Position updated from {topic}: x={self.robot_data['position']['x']:.2f}, y={self.robot_data['position']['y']:.2f}")

        except Exception as e:
            logger.error(f"Error extracting position from {topic}: {e}")
    
    def extract_battery_data(self, topic: str, msg: Dict[str, Any]):
        """Extract battery level from battery messages."""
        try:
            if topic in ["/battery_state", "/battery"]:
                # Standard battery state message
                percentage = msg.get("percentage", None)
                voltage = msg.get("voltage", None)
                current = msg.get("current", None)
                
                self.robot_data["battery_level"] = {
                    "percentage": percentage,
                    "voltage": voltage,
                    "current": current,
                    "source_topic": topic
                }
                
            elif topic == "/power_status":
                # Simple float message (assuming percentage)
                data_value = msg.get("data", None)
                
                self.robot_data["battery_level"] = {
                    "percentage": data_value,
                    "voltage": None,
                    "current": None,
                    "source_topic": topic
                }
            
            battery_info = self.robot_data["battery_level"]
            if battery_info["percentage"] is not None:
                logger.info(f"Battery updated from {topic}: {battery_info['percentage']:.1f}%")
            else:
                logger.info(f"Battery data updated from {topic}")

        except Exception as e:
            logger.error(f"Error extracting battery from {topic}: {e}")


async def retrieve_robot_data_from_ip(ip_address: str, port: int = 8765) -> Dict[str, Any]:
    """Standalone function to retrieve robot data from specific IP."""
    retriever = ROS2DataRetriever(ip_address, port)
    return await retriever.retrieve_robot_data()


async def main():
    """Main function for standalone testing."""
    if len(sys.argv) < 2:
        print("Usage: python ros2_data_retriever.py <ip_address> [port]")
        print("Example: python ros2_data_retriever.py ************* 8765")
        sys.exit(1)
    
    ip_address = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8765
    
    print("ROS2 Data Retriever")
    print("=" * 50)
    print(f"Target: {ip_address}:{port}")
    print("Retrieving: Robot position and battery level")
    print()
    
    try:
        result = await retrieve_robot_data_from_ip(ip_address, port)
        
        print("RETRIEVAL RESULTS")
        print("=" * 50)
        print(json.dumps(result, indent=2))
        
        return result
        
    except KeyboardInterrupt:
        print("\nRetrieval interrupted by user")
        return {}
    except Exception as e:
        print(f"\nRetrieval error: {e}")
        return {}


if __name__ == "__main__":
    asyncio.run(main())
