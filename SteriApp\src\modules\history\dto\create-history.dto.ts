import { Is<PERSON>tring, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateHistoryDto {
  @ApiProperty()
  @IsString()
  taskId: string

  @ApiProperty()
  @IsString()
  userId: string

  @ApiProperty()
  @IsString()
  robotId: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  zoneId?: string

  @ApiProperty()
  @IsString()
  action: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  duration?: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  result?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  errorMessage?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  batteryUsed?: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  disinfectionEfficiency?: number
}
