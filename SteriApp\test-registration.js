// Test script to debug registration process
const fetch = require('node-fetch');

async function testRegistration() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('Testing registration functionality...\n');
  
  // Generate a unique email for testing
  const timestamp = Date.now();
  const testEmail = `test${timestamp}@example.com`;
  const testPassword = 'testpass123';
  
  console.log(`Test email: ${testEmail}`);
  console.log(`Test password: ${testPassword}\n`);
  
  // Test registration
  console.log('Step 1: Testing user registration');
  try {
    const registerResponse = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        email: testEmail,
        password: testPassword,
        role: 'user'
      }),
    });
    
    const registerData = await registerResponse.json();
    
    if (registerResponse.ok) {
      console.log('✅ Registration successful');
      console.log('Response:', registerData);
      
      // Wait a moment for Firebase to process
      console.log('\nWaiting 2 seconds for Firebase to process...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Now test login with the same credentials
      console.log('\nStep 2: Testing login with registered credentials');
      
      const loginResponse = await fetch(`${baseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail,
          password: testPassword
        }),
      });
      
      const loginData = await loginResponse.json();
      
      if (loginResponse.ok) {
        console.log('✅ Login successful - Password was properly registered!');
        console.log('Login response:', loginData);
      } else {
        console.log('❌ Login failed - Password might not be registered properly');
        console.log('Login error:', loginData);
        
        // Test with wrong password to see if it gives different error
        console.log('\nStep 3: Testing login with wrong password');
        const wrongPasswordResponse = await fetch(`${baseUrl}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: testEmail,
            password: 'wrongpassword'
          }),
        });
        
        const wrongPasswordData = await wrongPasswordResponse.json();
        console.log('Wrong password response:', wrongPasswordData);
      }
      
    } else {
      console.log('❌ Registration failed');
      console.log('Error:', registerData);
    }
  } catch (error) {
    console.log('❌ Registration test failed with error:', error.message);
  }
}

// Check if server is running first
async function checkServer() {
  const baseUrl = 'http://localhost:3001';
  try {
    const response = await fetch(`${baseUrl}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}), // Empty body to trigger validation
    });
    
    // If we get any response (even validation error), server is running
    console.log('✅ Server is running\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the server with: npm run start:dev');
    console.log('Error:', error.message);
    return false;
  }
}

// Run the test
async function runTest() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testRegistration();
  }
}

runTest().catch(console.error);
