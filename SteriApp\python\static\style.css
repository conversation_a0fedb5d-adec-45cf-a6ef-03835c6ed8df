/* SteriBot Web Visualizer Styles */

body { 
    font-family: Arial, sans-serif; 
    margin: 0; 
    padding: 20px; 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
}

.container { 
    max-width: 1400px; 
    margin: 0 auto; 
}

.header { 
    background: rgba(255,255,255,0.1); 
    padding: 30px; 
    border-radius: 15px; 
    margin-bottom: 30px; 
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.header h1 { 
    margin: 0; 
    font-size: 2.5em; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    margin: 10px 0 0 0;
    font-size: 1.2em;
    opacity: 0.9;
}

.status-bar { 
    display: flex; 
    gap: 20px; 
    margin-bottom: 30px; 
    flex-wrap: wrap;
}

.status-card { 
    background: rgba(255,255,255,0.15); 
    padding: 20px; 
    border-radius: 10px; 
    flex: 1; 
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    min-width: 150px;
}

.status-card h3 { 
    margin: 0 0 10px 0; 
    font-size: 1.1em; 
    opacity: 0.9;
}

.status-value { 
    font-size: 1.8em; 
    font-weight: bold; 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.data-grid { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); 
    gap: 25px; 
}

.data-card { 
    background: rgba(255,255,255,0.1); 
    padding: 25px; 
    border-radius: 15px; 
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.data-card h3 { 
    margin: 0 0 15px 0; 
    font-size: 1.3em; 
    border-bottom: 2px solid rgba(255,255,255,0.3); 
    padding-bottom: 10px; 
}

.data-content { 
    background: rgba(0,0,0,0.3); 
    padding: 20px; 
    border-radius: 8px; 
    font-family: 'Courier New', monospace; 
    font-size: 12px; 
    max-height: 300px; 
    overflow-y: auto; 
    white-space: pre-wrap;
    border: 1px solid rgba(255,255,255,0.1);
}

.timestamp { 
    color: rgba(255,255,255,0.7); 
    font-size: 11px; 
    margin-bottom: 10px; 
    font-style: italic;
}

.connected { 
    color: #4ade80; 
    font-weight: bold; 
}

.disconnected { 
    color: #f87171; 
    font-weight: bold; 
}

.no-data { 
    color: rgba(255,255,255,0.5); 
    font-style: italic; 
    text-align: center;
    padding: 40px;
}

.foxglove-info {
    background: rgba(33, 150, 243, 0.2);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #2196F3;
    backdrop-filter: blur(10px);
}

/* Map specific styles */
.map-card {
    grid-column: span 2;
    min-height: 500px;
}

.map-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.map-info {
    background: rgba(0,0,0,0.2);
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 12px;
}

.map-canvas-container {
    flex: 1;
    background: rgba(0,0,0,0.3);
    border-radius: 8px;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

#mapCanvas {
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 5px;
    background: #ffffff;
    max-width: 100%;
    max-height: 100%;
}

.map-controls {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
}

.map-control-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s;
}

.map-control-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .status-bar {
        flex-direction: column;
    }
    
    .data-grid {
        grid-template-columns: 1fr;
    }
    
    .map-card {
        grid-column: span 1;
    }
    
    .header h1 {
        font-size: 2em;
    }
}

/* Scrollbar styling */
.data-content::-webkit-scrollbar {
    width: 8px;
}

.data-content::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
}

.data-content::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 4px;
}

.data-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* Animation for data updates */
.data-card {
    transition: transform 0.2s ease;
}

.data-card:hover {
    transform: translateY(-2px);
}

.status-value {
    transition: color 0.3s ease;
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.loading {
    animation: pulse 2s infinite;
}
