import { Module } from "@nestjs/common"
import { UsersController } from "./users.controller"
import { UsersService } from "./users.service"
import { FirebaseModule } from "../../config/firebase/firebase.module"
import { FileValidationService } from "../../common/validators/file-validation.service"

@Module({
  imports: [FirebaseModule],
  controllers: [UsersController],
  providers: [UsersService, FileValidationService],
  exports: [UsersService],
})
export class UsersModule {}
