# Simple ROS2 Connection API

## 🎯 **Overview**

This is a simple and clean API implementation that allows the frontend to connect to available ROS2 IP addresses. It integrates with the existing `scan_ros2.py` script and provides a straightforward connection testing endpoint.

## 📁 **Module Structure**

```
src/modules/ros2-connection/
├── ros2-connection.module.ts          # NestJS module definition
├── ros2-connection.controller.ts      # REST API endpoints
├── ros2-connection.service.ts         # Business logic
└── README.md                         # This documentation
```

## 🚀 **API Endpoint**

### **GET /api/connect-ros2**

**Purpose**: Get ROS2 IPs from scan.py and test connections to each one.

**Response Format**:
```json
{
  "ips": [
    {"ip": "************", "status": "connected"},
    {"ip": "************", "status": "failed"}
  ]
}
```

**How it works**:
1. Calls the existing `scan_ros2.py` script (without modifying it)
2. Parses the output to extract IP addresses
3. Tests WebSocket connection to each IP on port 8765
4. Returns connection status for each IP

## 🔧 **Integration Setup**

### **1. Add Module to App Module**

Add the ROS2ConnectionModule to your main app module:

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ROS2ConnectionModule } from './modules/ros2-connection/ros2-connection.module';

@Module({
  imports: [
    // ... other modules
    ROS2ConnectionModule,
  ],
  // ...
})
export class AppModule {}
```

### **2. Install Required Dependencies**

Make sure you have the WebSocket library installed:

```bash
npm install ws
npm install @types/ws --save-dev
```

### **3. Verify scan_ros2.py is Available**

The service expects the scan script at: `Ros_api/scan_ros2.py`

Test it manually:
```bash
python Ros_api/scan_ros2.py
# Should output: ws://*************:8765 (or similar)
```

## 🧪 **Testing with Postman**

### **1. Start Your NestJS Server**
```bash
npm run start:dev
```

### **2. Test the API Endpoint**

**Request**:
```
GET http://localhost:3000/api/connect-ros2
```

**Expected Response**:
```json
{
  "ips": [
    {"ip": "*************", "status": "connected"}
  ]
}
```

### **3. Test Health Check**

**Request**:
```
GET http://localhost:3000/api/ros2-health
```

**Expected Response**:
```json
{
  "status": "healthy",
  "scan_script_exists": true
}
```

## 🎮 **Frontend Integration**

### **Connect Button Implementation**

```typescript
// Frontend service example
async connectToROS2() {
  try {
    const response = await fetch('/api/connect-ros2');
    const data = await response.json();
    
    // Handle the response
    data.ips.forEach(ip => {
      if (ip.status === 'connected') {
        console.log(`✅ Connected to ROS2 at ${ip.ip}`);
        // Update UI to show connected robot
      } else {
        console.log(`❌ Failed to connect to ${ip.ip}`);
        // Update UI to show failed connection
      }
    });
    
    return data;
  } catch (error) {
    console.error('Failed to connect to ROS2:', error);
    throw error;
  }
}
```

### **React Component Example**

```jsx
function ROS2ConnectButton() {
  const [connecting, setConnecting] = useState(false);
  const [results, setResults] = useState(null);

  const handleConnect = async () => {
    setConnecting(true);
    try {
      const response = await fetch('/api/connect-ros2');
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Connection failed:', error);
    } finally {
      setConnecting(false);
    }
  };

  return (
    <div>
      <button onClick={handleConnect} disabled={connecting}>
        {connecting ? 'Connecting...' : 'Connect to ROS2'}
      </button>
      
      {results && (
        <div>
          <h3>Connection Results:</h3>
          {results.ips.map(ip => (
            <div key={ip.ip}>
              {ip.ip}: {ip.status === 'connected' ? '✅' : '❌'} {ip.status}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

## 🔍 **How It Works**

### **Step-by-Step Process**

1. **Frontend clicks "Connect"** → Calls `GET /api/connect-ros2`

2. **Backend receives request** → `ROS2ConnectionController.connectToROS2()`

3. **Service calls scan.py** → `python Ros_api/scan_ros2.py`

4. **Parse scan output** → Extract IPs from `ws://*************:8765` format

5. **Test each IP** → Try WebSocket connection to `ws://[IP]:8765`

6. **Return results** → JSON response with connection status

### **Example Flow**

```
Frontend: "Connect" button clicked
    ↓
Backend: GET /api/connect-ros2
    ↓
Service: Execute scan_ros2.py
    ↓ 
Output: "ws://*************:8765"
    ↓
Service: Extract IP "*************"
    ↓
Service: Test WebSocket connection
    ↓
Result: {"ip": "*************", "status": "connected"}
    ↓
Frontend: Display connection result
```

## ⚡ **Key Features**

- ✅ **Simple and Clean**: Minimal implementation focused on core requirement
- ✅ **Reuses scan.py**: No modifications to existing scan script
- ✅ **No Hardcoded IPs**: Uses only IPs discovered by scan.py
- ✅ **Real Connection Testing**: Actually tests WebSocket connections
- ✅ **Frontend Ready**: Perfect for connect button integration
- ✅ **Postman Testable**: Easy to test with REST client

## 🚨 **Error Handling**

The API handles various error scenarios:

- **Scan script fails**: Returns empty IP list
- **No IPs found**: Returns `{"ips": []}`
- **Connection timeouts**: Returns `{"ip": "x.x.x.x", "status": "failed"}`
- **WebSocket errors**: Returns `{"ip": "x.x.x.x", "status": "failed"}`

## 📊 **Performance**

- **Scan time**: ~5-10 seconds (depends on network size)
- **Connection test**: 5 second timeout per IP
- **Parallel testing**: Tests all IPs simultaneously
- **Total time**: Usually 5-15 seconds for complete process

## 🎯 **Perfect for Your Use Case**

This implementation is exactly what you requested:

1. ✅ **Uses scan.py** to get ROS2 IPs (without modifying it)
2. ✅ **Tests connections** to each discovered IP
3. ✅ **Returns simple JSON** with connection status
4. ✅ **Integrates with backend** in `steribot/src`
5. ✅ **Works with frontend** connect button
6. ✅ **Testable with Postman** using simple GET request
7. ✅ **No hardcoded IPs** - only uses discovered ones

Ready to integrate and test! 🚀
