export interface ROS2ConnectionResult {
    ip: string;
    status: 'connected' | 'failed';
}
export interface ROS2ConnectionResponse {
    ips: ROS2ConnectionResult[];
}
export declare class ROS2ConnectionService {
    private readonly scanScriptPath;
    connectToROS2IPs(): Promise<ROS2ConnectionResponse>;
    healthCheck(): {
        status: string;
        scan_script_exists: any;
    };
    private getIPsFromScan;
    private testConnections;
    private testConnection;
}
