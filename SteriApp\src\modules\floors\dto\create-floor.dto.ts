import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateFloorDto {
  @ApiProperty()
  @IsNumber()
  floorNumber: number

  @ApiProperty()
  @IsString()
  buildingId: string

  @ApiProperty()
  @IsString()
  floorName: string

  @ApiProperty()
  @IsNumber()
  totalRooms: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  mapData?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean
}
