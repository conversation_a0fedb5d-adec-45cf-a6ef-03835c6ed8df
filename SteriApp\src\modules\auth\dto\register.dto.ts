import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class RegisterDto {
  @ApiProperty({
    description: 'Username for the user',
    example: 'walid',
    minLength: 3
  })
  @IsString({ message: 'Username must be a string' })
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  username: string

  @ApiProperty({
    description: 'User first name',
    example: 'John'
  })
  @IsString({ message: 'First name must be a string' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  firstName: string

  @ApiProperty({
    description: 'User last name',
    example: 'Doe'
  })
  @IsString({ message: 'Last name must be a string' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  lastName: string

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string

  @ApiProperty({
    description: 'Password for the user account',
    example: 'securePass1',
    minLength: 6
  })
  @IsString({ message: 'Password must be a string' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string

  @ApiProperty({
    enum: UserRole,
    required: false,
    description: 'Role of the user',
    example: UserRole.USER
  })
  @IsOptional()
  @IsEnum(UserRole, { message: 'Role must be either user or admin' })
  role?: UserRole

  @ApiProperty({
    required: false,
    description: 'Preferred language',
    example: 'en'
  })
  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  language?: string

  @ApiProperty({
    required: false,
    description: 'Job title ID',
    example: 'jt_123'
  })
  @IsOptional()
  @IsString({ message: 'Job title ID must be a string' })
  jobTitleId?: string

  @ApiProperty({
    required: false,
    description: 'Department ID',
    example: 'dept_123'
  })
  @IsOptional()
  @IsString({ message: 'Department ID must be a string' })
  departmentId?: string

  @ApiProperty({
    required: false,
    description: 'User profile picture URL',
    example: 'https://example.com/profile.jpg'
  })
  @IsOptional()
  @IsString({ message: 'Picture URL must be a string' })
  picture?: string
}
