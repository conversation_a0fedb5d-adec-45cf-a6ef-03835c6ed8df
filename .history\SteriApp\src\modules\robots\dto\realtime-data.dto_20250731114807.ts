import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsIP, IsBoolean } from 'class-validator';

// Connection Management DTOs
export class StartRealtimeStreamDto {
  @ApiProperty({
    description: 'IP address of the ROS2 bridge to connect to',
    example: '*************',
  })
  @IsString()
  @IsIP()
  ip_address: string;

  @ApiProperty({
    description: 'Port of the ROS2 bridge (optional, defaults to 8765)',
    example: 8765,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiProperty({
    description: 'Include robot position and battery data in stream',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  include_robot_data?: boolean;

  @ApiProperty({
    description: 'Include map data in stream',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  include_map_data?: boolean;

  @ApiProperty({
    description: 'Stream update frequency in Hz (1-10)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  update_frequency?: number;
}

export class StopRealtimeStreamDto {
  @ApiProperty({
    description: 'Stream session ID to stop',
    example: 'stream_*************_8765',
  })
  @IsString()
  session_id: string;
}

// Real-time Event DTOs
export class RealtimeConnectionEventDto {
  @ApiProperty({
    description: 'Event type',
    enum: ['connected', 'disconnected', 'connecting', 'error', 'reconnecting'],
    example: 'connected',
  })
  event: 'connected' | 'disconnected' | 'connecting' | 'error' | 'reconnecting';

  @ApiProperty({
    description: 'Stream session ID',
    example: 'stream_*************_8765',
  })
  session_id: string;

  @ApiProperty({
    description: 'IP address being streamed',
    example: '*************',
  })
  ip_address: string;

  @ApiProperty({
    description: 'Port being used',
    example: 8765,
  })
  port: number;

  @ApiProperty({
    description: 'Timestamp of the event',
    example: '2025-07-30T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Error message if event is error',
    required: false,
  })
  error_message?: string;
}

export class RealtimeRobotDataEventDto {
  @ApiProperty({
    description: 'Event type',
    example: 'robot_data',
  })
  event: 'robot_data';

  @ApiProperty({
    description: 'Stream session ID',
    example: 'stream_*************_8765',
  })
  session_id: string;

  @ApiProperty({
    description: 'Robot position data',
  })
  position?: {
    x: number;
    y: number;
    z: number;
    orientation: {
      x: number;
      y: number;
      z: number;
      w: number;
    };
    source_topic: string;
  };

  @ApiProperty({
    description: 'Battery level data',
  })
  battery_level?: {
    percentage?: number;
    voltage?: number;
    current?: number;
    source_topic: string;
  };

  @ApiProperty({
    description: 'Timestamp when data was received',
    example: '2025-07-30T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Data sequence number for ordering',
    example: 1234,
  })
  sequence: number;
}

export class RealtimeMapDataEventDto {
  @ApiProperty({
    description: 'Event type',
    example: 'map_data',
  })
  event: 'map_data';

  @ApiProperty({
    description: 'Stream session ID',
    example: 'stream_*************_8765',
  })
  session_id: string;

  @ApiProperty({
    description: 'Map metadata information',
  })
  info?: {
    width: number;
    height: number;
    resolution: number;
    origin: {
      position: { x: number; y: number; z: number };
      orientation: { x: number; y: number; z: number; w: number };
    };
    map_load_time: string;
  };

  @ApiProperty({
    description: 'Occupancy grid data array (compressed for real-time)',
    type: [Number],
    required: false,
  })
  data?: number[];

  @ApiProperty({
    description: 'Map statistics',
  })
  statistics?: {
    total_cells: number;
    free_cells: number;
    occupied_cells: number;
    unknown_cells: number;
    free_percentage: number;
    occupied_percentage: number;
    unknown_percentage: number;
  };

  @ApiProperty({
    description: 'Whether this is a full map update or incremental',
    example: true,
  })
  is_full_update: boolean;

  @ApiProperty({
    description: 'Timestamp when map data was received',
    example: '2025-07-30T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'Map data sequence number',
    example: 42,
  })
  sequence: number;

  @ApiProperty({
    description: 'Size of the map data array',
    example: 147456,
  })
  data_length: number;
}

export class RealtimeStreamStatusDto {
  @ApiProperty({
    description: 'Stream session ID',
    example: 'stream_*************_8765',
  })
  session_id: string;

  @ApiProperty({
    description: 'Current connection status',
    enum: ['connected', 'disconnected', 'connecting', 'error'],
    example: 'connected',
  })
  status: 'connected' | 'disconnected' | 'connecting' | 'error';

  @ApiProperty({
    description: 'IP address being streamed',
    example: '*************',
  })
  ip_address: string;

  @ApiProperty({
    description: 'Port being used',
    example: 8765,
  })
  port: number;

  @ApiProperty({
    description: 'Whether robot data is included',
    example: true,
  })
  include_robot_data: boolean;

  @ApiProperty({
    description: 'Whether map data is included',
    example: true,
  })
  include_map_data: boolean;

  @ApiProperty({
    description: 'Current update frequency in Hz',
    example: 2,
  })
  update_frequency: number;

  @ApiProperty({
    description: 'Stream start time',
    example: '2025-07-30T10:30:00.000Z',
  })
  started_at: string;

  @ApiProperty({
    description: 'Last data received timestamp',
    example: '2025-07-30T10:30:15.000Z',
    required: false,
  })
  last_data_received?: string;

  @ApiProperty({
    description: 'Total messages received',
    example: 150,
  })
  messages_received: number;

  @ApiProperty({
    description: 'Number of connected clients',
    example: 3,
  })
  connected_clients: number;
}

// WebSocket Event Types (for type safety)
export type RealtimeEventType = 
  | 'connection'
  | 'robot_data' 
  | 'map_data'
  | 'stream_status'
  | 'error';

export type RealtimeEventData = 
  | RealtimeConnectionEventDto
  | RealtimeRobotDataEventDto
  | RealtimeMapDataEventDto
  | RealtimeStreamStatusDto;

// Stream Configuration
export interface StreamConfig {
  ip_address: string;
  port: number;
  include_robot_data: boolean;
  include_map_data: boolean;
  update_frequency: number;
  session_id: string;
}
