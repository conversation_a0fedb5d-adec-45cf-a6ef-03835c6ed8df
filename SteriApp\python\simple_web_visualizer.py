#!/usr/bin/env python3
"""
Simple Web Visualizer for SteriBot
Connects to ROS2 bridge and serves data via simple HTTP server
Works alongside robot_data_bridge.py (which handles Foxglove Studio)
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import time
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleRobotDataCollector:
    """Simple data collector that connects to ROS2 bridge."""
    
    def __init__(self, ros2_bridge_url="ws://192.168.1.165:8765"):
        self.ros2_bridge_url = ros2_bridge_url
        self.ros2_websocket = None
        self.is_connected = False
        
        # Latest data storage
        self.latest_data = {
            "/cmd_vel": None,
            "/imu": None,
            "/joint_states": None,
            "/odom": None,
            "/scan": None,
            "/map": None
        }
        
        # Statistics
        self.messages_received = 0
        self.start_time = datetime.now()
        
        logger.info("🤖 Simple Robot Data Collector initialized")
        logger.info(f"🔗 ROS2 Bridge: {self.ros2_bridge_url}")

    async def connect_and_collect(self):
        """Connect to ROS2 and start collecting data."""
        try:
            logger.info(f"🔗 Connecting to ROS2 bridge: {self.ros2_bridge_url}")
            
            self.ros2_websocket = await websockets.connect(self.ros2_bridge_url)
            self.is_connected = True
            
            logger.info("✅ Connected to ROS2 bridge!")
            
            # Subscribe to topics (same as robot_data_bridge.py but without TF topics)
            topics_with_types = [
                ("/cmd_vel", "geometry_msgs/msg/Twist"),
                ("/imu", "sensor_msgs/msg/Imu"),
                ("/joint_states", "sensor_msgs/msg/JointState"),
                ("/odom", "nav_msgs/msg/Odometry"),
                ("/scan", "sensor_msgs/msg/LaserScan"),
                ("/map", "nav_msgs/msg/OccupancyGrid")
            ]
            
            for topic, msg_type in topics_with_types:
                subscribe_msg = {
                    "op": "subscribe",
                    "topic": topic,
                    "type": msg_type,
                    "throttle_rate": 100  # 10 Hz max
                }
                await self.ros2_websocket.send(json.dumps(subscribe_msg))
                logger.info(f"📡 Subscribed to: {topic}")
            
            # Listen for data
            await self.listen_for_data()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to ROS2: {e}")
            self.is_connected = False

    async def listen_for_data(self):
        """Listen for ROS2 data."""
        logger.info("👂 Listening for ROS2 data...")
        
        try:
            async for message in self.ros2_websocket:
                await self.process_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ ROS2 connection closed")
            self.is_connected = False
        except Exception as e:
            logger.error(f"❌ Error listening: {e}")

    async def process_message(self, message):
        """Process ROS2 message."""
        try:
            data = json.loads(message)

            if "topic" in data and "msg" in data:
                topic = data["topic"]
                msg = data["msg"]

                # Store latest data with special processing for map
                if topic in self.latest_data:
                    if topic == "/map":
                        # Process map data for visualization
                        processed_map = self.process_map_data(msg)
                        self.latest_data[topic] = {
                            "timestamp": datetime.now().isoformat(),
                            "data": processed_map
                        }
                    else:
                        self.latest_data[topic] = {
                            "timestamp": datetime.now().isoformat(),
                            "data": msg
                        }

                self.messages_received += 1

                if self.messages_received % 100 == 0:
                    logger.info(f"📊 Received {self.messages_received} messages")

        except json.JSONDecodeError:
            pass
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")

    def process_map_data(self, map_msg):
        """Process map data for web visualization."""
        try:
            info = map_msg.get("info", {})
            map_data = map_msg.get("data", [])

            # Extract map information
            width = info.get("width", 0)
            height = info.get("height", 0)
            resolution = info.get("resolution", 0.0)
            origin = info.get("origin", {})

            # Process map data for visualization
            processed = {
                "info": {
                    "width": width,
                    "height": height,
                    "resolution": resolution,
                    "origin": origin
                },
                "data": map_data,
                "data_length": len(map_data) if isinstance(map_data, list) else 0,
                "has_data": len(map_data) > 0 if isinstance(map_data, list) else False,
                "summary": f"Map: {width}x{height} pixels, {resolution}m/pixel"
            }

            logger.info(f"🗺️ Processed map: {width}x{height}, {len(map_data)} data points")
            return processed

        except Exception as e:
            logger.error(f"❌ Error processing map data: {e}")
            return {"error": str(e), "has_data": False}

    def get_web_data(self):
        """Get data formatted for web interface."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "status": "connected" if self.is_connected else "disconnected",
            "messages_received": self.messages_received,
            "uptime": uptime,
            "timestamp": datetime.now().isoformat(),
            "data": self.latest_data
        }


class SimpleWebHandler(SimpleHTTPRequestHandler):
    """HTTP handler for simple web interface."""
    
    def __init__(self, *args, data_collector=None, **kwargs):
        self.data_collector = data_collector
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """Handle GET requests."""
        if self.path == '/':
            self.serve_web_interface()
        elif self.path == '/api/data':
            self.serve_robot_data()
        elif self.path.startswith('/static/'):
            self.serve_static_file()
        else:
            self.send_error(404)

    def serve_web_interface(self):
        """Serve the main web interface."""
        try:
            # Get the directory where this script is located
            script_dir = os.path.dirname(os.path.abspath(__file__))
            html_path = os.path.join(script_dir, 'templates', 'index.html')

            with open(html_path, 'r', encoding='utf-8') as f:
                html = f.read()

            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(html.encode())

        except FileNotFoundError:
            # Fallback to simple HTML if template file not found
            html = '''
<!DOCTYPE html>
<html>
<head>
    <title>SteriBot Web Visualizer - Template Missing</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 50px; background: #f0f0f0; }
        .error { background: #ffebee; padding: 20px; border-radius: 8px; color: #c62828; }
    </style>
</head>
<body>
    <div class="error">
        <h1>Template File Missing</h1>
        <p>Could not find templates/index.html. Please ensure the template files are in the correct location.</p>
        <p>Expected path: templates/index.html</p>
    </div>
</body>
</html>
            '''
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(html.encode())

    def serve_static_file(self):
        """Serve static files (CSS, JS)."""
        try:
            # Get the directory where this script is located
            script_dir = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(script_dir, self.path[1:])  # Remove leading '/'

            if os.path.exists(file_path) and os.path.isfile(file_path):
                # Determine content type
                if file_path.endswith('.css'):
                    content_type = 'text/css'
                elif file_path.endswith('.js'):
                    content_type = 'application/javascript'
                else:
                    content_type = 'text/plain'

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.end_headers()
                self.wfile.write(content.encode())
            else:
                self.send_error(404)

        except Exception as e:
            logger.error(f"Error serving static file {self.path}: {e}")
            self.send_error(500)

    def serve_robot_data(self):
        """Serve robot data as JSON."""
        data = self.data_collector.get_web_data() if self.data_collector else {"error": "No data collector"}
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())


def create_web_handler(data_collector):
    """Create web handler with data collector reference."""
    def handler(*args, **kwargs):
        return SimpleWebHandler(*args, data_collector=data_collector, **kwargs)
    return handler


async def main():
    """Main function."""
    print("🌐 SteriBot Simple Web Visualizer")
    print("=" * 50)
    print("🎯 Purpose: Simple web interface for robot data")
    print("📡 Works alongside robot_data_bridge.py (Foxglove Studio)")
    print("🌐 Web Interface: http://localhost:8082")
    print("📊 API Endpoint: http://localhost:8082/api/data")
    print()
    
    # Create data collector
    data_collector = SimpleRobotDataCollector()
    
    # Start HTTP server in background thread
    web_handler = create_web_handler(data_collector)
    httpd = HTTPServer(('localhost', 8082), web_handler)
    http_thread = threading.Thread(target=httpd.serve_forever)
    http_thread.daemon = True
    http_thread.start()
    
    print("✅ Web server started on http://localhost:8082")
    print("🔗 robot_data_bridge.py handles Foxglove Studio on ws://localhost:8768")
    print("🔗 Connecting to ROS2...")
    
    try:
        # Connect to ROS2 and start collecting data
        await data_collector.connect_and_collect()
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopping web visualizer...")
        httpd.shutdown()
        
        stats = data_collector.get_web_data()
        print(f"\n📊 Final Stats:")
        print(f"   Messages received: {stats['messages_received']}")
        print(f"   Uptime: {stats['uptime']:.1f}s")
        
        print("👋 Web visualizer stopped")


if __name__ == "__main__":
    asyncio.run(main())
