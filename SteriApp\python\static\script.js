// SteriBot Web Visualizer JavaScript

// Global variables for map visualization
let mapCanvas = null;
let mapCtx = null;
let currentMapData = null;
let mapZoom = 1;
let mapOffsetX = 0;
let mapOffsetY = 0;
let showGrid = false;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🤖 SteriBot Web Visualizer loaded successfully!');
    console.log('📡 Foxglove Studio: ws://localhost:8768');
    console.log('🌐 Web Interface: This page');
    
    // Initialize map canvas
    initializeMapCanvas();
    
    // Start data updates
    updateData();
    setInterval(updateData, 1000);
});

function initializeMapCanvas() {
    mapCanvas = document.getElementById('mapCanvas');
    if (mapCanvas) {
        mapCtx = mapCanvas.getContext('2d');
        
        // Add mouse wheel zoom
        mapCanvas.addEventListener('wheel', function(e) {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            mapZoom *= delta;
            mapZoom = Math.max(0.1, Math.min(5, mapZoom));
            drawMap();
        });
        
        // Add mouse drag
        let isDragging = false;
        let lastX, lastY;
        
        mapCanvas.addEventListener('mousedown', function(e) {
            isDragging = true;
            lastX = e.clientX;
            lastY = e.clientY;
        });
        
        mapCanvas.addEventListener('mousemove', function(e) {
            if (isDragging) {
                const deltaX = e.clientX - lastX;
                const deltaY = e.clientY - lastY;
                mapOffsetX += deltaX;
                mapOffsetY += deltaY;
                lastX = e.clientX;
                lastY = e.clientY;
                drawMap();
            }
        });
        
        mapCanvas.addEventListener('mouseup', function() {
            isDragging = false;
        });
        
        mapCanvas.addEventListener('mouseleave', function() {
            isDragging = false;
        });
    }
}

function updateData() {
    fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            // Update status
            const statusElement = document.getElementById('connectionStatus');
            if (data.status === 'connected') {
                statusElement.innerHTML = '<span class="connected">🟢 Connected</span>';
            } else {
                statusElement.innerHTML = '<span class="disconnected">🔴 Disconnected</span>';
            }
            
            document.getElementById('messageCount').textContent = data.messages_received.toLocaleString();
            document.getElementById('uptime').textContent = Math.round(data.uptime) + 's';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            
            // Update topic data
            updateTopicData('odom', data.data['/odom']);
            updateTopicData('scan', data.data['/scan']);
            updateTopicData('imu', data.data['/imu']);
            updateTopicData('cmdVel', data.data['/cmd_vel']);
            updateTopicData('jointStates', data.data['/joint_states']);
            updateMapData(data.data['/map']);
        })
        .catch(error => {
            console.error('Error fetching data:', error);
            document.getElementById('connectionStatus').innerHTML = 
                '<span class="disconnected">🔴 Error</span>';
        });
}

function updateTopicData(topicName, topicData) {
    const dataElement = document.getElementById(topicName + 'Data');
    const timeElement = document.getElementById(topicName + 'Time');
    
    if (topicData && topicData.data) {
        // Format the data nicely
        const formattedData = JSON.stringify(topicData.data, null, 2);
        dataElement.textContent = formattedData;
        if (timeElement) {
            timeElement.textContent = 'Last updated: ' + new Date(topicData.timestamp).toLocaleTimeString();
        }
    } else {
        dataElement.innerHTML = '<div class="no-data">No data received yet</div>';
        if (timeElement) {
            timeElement.textContent = '';
        }
    }
}

function updateMapData(mapData) {
    const mapInfoElement = document.getElementById('mapInfo');
    
    if (mapData && mapData.data && mapData.data.has_data) {
        currentMapData = mapData.data;
        
        // Update map info
        const info = currentMapData.info;
        mapInfoElement.innerHTML = `
            <strong>Map Info:</strong> ${info.width}x${info.height} pixels, 
            Resolution: ${info.resolution.toFixed(3)}m/pixel, 
            Data points: ${currentMapData.data_length.toLocaleString()}
            <br><strong>Last updated:</strong> ${new Date(mapData.timestamp).toLocaleTimeString()}
        `;
        
        // Draw the map
        drawMap();
    } else {
        mapInfoElement.innerHTML = '<div class="no-data">No map data received yet</div>';
        clearMap();
    }
}

function drawMap() {
    if (!currentMapData || !currentMapData.has_data || !mapCtx) {
        return;
    }
    
    const canvas = mapCanvas;
    const ctx = mapCtx;
    const info = currentMapData.info;
    const mapDataArray = currentMapData.data;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Calculate scaling
    const mapWidth = info.width;
    const mapHeight = info.height;
    const scaleX = (canvas.width * 0.8) / mapWidth;
    const scaleY = (canvas.height * 0.8) / mapHeight;
    const scale = Math.min(scaleX, scaleY) * mapZoom;
    
    // Calculate center position
    const centerX = canvas.width / 2 + mapOffsetX;
    const centerY = canvas.height / 2 + mapOffsetY;
    const startX = centerX - (mapWidth * scale) / 2;
    const startY = centerY - (mapHeight * scale) / 2;
    
    // Draw grid if enabled
    if (showGrid) {
        drawGrid(ctx, canvas, scale);
    }
    
    // Draw map data
    const imageData = ctx.createImageData(mapWidth, mapHeight);
    const data = imageData.data;
    
    for (let i = 0; i < mapDataArray.length; i++) {
        const value = mapDataArray[i];
        const pixelIndex = i * 4;
        
        // Map occupancy values to colors
        if (value === -1) {
            // Unknown - gray
            data[pixelIndex] = 128;     // R
            data[pixelIndex + 1] = 128; // G
            data[pixelIndex + 2] = 128; // B
            data[pixelIndex + 3] = 255; // A
        } else if (value === 0) {
            // Free space - white
            data[pixelIndex] = 255;     // R
            data[pixelIndex + 1] = 255; // G
            data[pixelIndex + 2] = 255; // B
            data[pixelIndex + 3] = 255; // A
        } else {
            // Occupied - black
            data[pixelIndex] = 0;       // R
            data[pixelIndex + 1] = 0;   // G
            data[pixelIndex + 2] = 0;   // B
            data[pixelIndex + 3] = 255; // A
        }
    }
    
    // Create temporary canvas for the image data
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = mapWidth;
    tempCanvas.height = mapHeight;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.putImageData(imageData, 0, 0);
    
    // Draw scaled image to main canvas
    ctx.imageSmoothingEnabled = false; // Keep pixels crisp
    ctx.drawImage(tempCanvas, startX, startY, mapWidth * scale, mapHeight * scale);
    
    // Draw border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.lineWidth = 2;
    ctx.strokeRect(startX, startY, mapWidth * scale, mapHeight * scale);
    
    // Draw info overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 200, 60);
    ctx.fillStyle = 'white';
    ctx.font = '12px Arial';
    ctx.fillText(`Zoom: ${mapZoom.toFixed(1)}x`, 20, 30);
    ctx.fillText(`Size: ${mapWidth}x${mapHeight}`, 20, 45);
    ctx.fillText(`Resolution: ${info.resolution.toFixed(3)}m/px`, 20, 60);
}

function drawGrid(ctx, canvas, scale) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 1;
    
    const gridSize = 50 * scale;
    
    // Vertical lines
    for (let x = 0; x < canvas.width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < canvas.height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
}

function clearMap() {
    if (mapCtx) {
        mapCtx.clearRect(0, 0, mapCanvas.width, mapCanvas.height);
        mapCtx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        mapCtx.fillRect(0, 0, mapCanvas.width, mapCanvas.height);
        mapCtx.fillStyle = 'rgba(255, 255, 255, 0.5)';
        mapCtx.font = '16px Arial';
        mapCtx.textAlign = 'center';
        mapCtx.fillText('Waiting for map data...', mapCanvas.width / 2, mapCanvas.height / 2);
        mapCtx.textAlign = 'left';
    }
}

// Map control functions
function zoomIn() {
    mapZoom *= 1.2;
    mapZoom = Math.min(5, mapZoom);
    drawMap();
}

function zoomOut() {
    mapZoom *= 0.8;
    mapZoom = Math.max(0.1, mapZoom);
    drawMap();
}

function resetZoom() {
    mapZoom = 1;
    mapOffsetX = 0;
    mapOffsetY = 0;
    drawMap();
}

function toggleGrid() {
    showGrid = !showGrid;
    drawMap();
}
