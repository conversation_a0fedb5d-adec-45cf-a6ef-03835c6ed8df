#!/usr/bin/env python3
"""
Enhanced Real-time Robot Data Bridge for SteriBot
Combines robot data and map streaming using WebSocket connections
Integrates with NestJS backend for real-time data streaming
Compatible with existing Foxglove SDK infrastructure
"""

import asyncio
import json
import logging
import websockets
import argparse
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional, Set
import signal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealtimeRobotBridge:
    def __init__(self, ros2_ip: str, ros2_port: int = 8765, session_id: str = None, 
                 update_frequency: float = 2.0, include_robot_data: bool = True, 
                 include_map_data: bool = True):
        self.ros2_bridge_url = f"ws://{ros2_ip}:{ros2_port}"
        self.session_id = session_id or f"stream_{ros2_ip}_{ros2_port}_{int(time.time())}"
        self.update_frequency = update_frequency
        self.include_robot_data = include_robot_data
        self.include_map_data = include_map_data
        
        # Connection management
        self.ros2_websocket = None
        self.is_connected = False
        self.should_stop = False
        
        # Data storage for latest values
        self.latest_data = {
            "/odom": None,
            "/battery_state": None,
            "/robot_pose": None,
            "/map": None,
            "/cmd_vel": None,
            "/imu": None,
            "/scan": None,
        }
        
        # Statistics
        self.messages_received = 0
        self.last_robot_data_time = None
        self.last_map_data_time = None
        self.sequence_number = 0
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"🛑 Received signal {signum}, shutting down...")
        self.should_stop = True

    async def start_streaming(self):
        """Main method to start real-time streaming."""
        try:
            logger.info(f"🚀 Starting realtime bridge: {self.session_id}")
            logger.info(f"🔗 ROS2 Bridge: {self.ros2_bridge_url}")
            logger.info(f"📊 Update frequency: {self.update_frequency} Hz")
            logger.info(f"🤖 Include robot data: {self.include_robot_data}")
            logger.info(f"🗺️ Include map data: {self.include_map_data}")
            
            # Send initial connection status
            self.send_output({
                "type": "connection",
                "status": "connecting",
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            })
            
            # Connect to ROS2 bridge
            await self.connect_to_ros2()
            
        except Exception as e:
            logger.error(f"❌ Failed to start streaming: {e}")
            self.send_output({
                "type": "error",
                "message": str(e),
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            })
            sys.exit(1)

    async def connect_to_ros2(self):
        """Connect to ROS2 bridge and start data collection."""
        retry_count = 0
        max_retries = 5
        
        while not self.should_stop and retry_count < max_retries:
            try:
                logger.info(f"🔗 Connecting to ROS2 bridge (attempt {retry_count + 1})...")
                
                self.ros2_websocket = await websockets.connect(
                    self.ros2_bridge_url,
                    timeout=10
                )
                self.is_connected = True
                
                logger.info("✅ Connected to ROS2 bridge!")
                
                # Send connection success
                self.send_output({
                    "type": "connection",
                    "status": "connected",
                    "session_id": self.session_id,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Subscribe to topics
                await self.subscribe_to_topics()
                
                # Start data collection and streaming
                await asyncio.gather(
                    self.listen_for_ros2_data(),
                    self.stream_data_periodically()
                )
                
                break
                
            except Exception as e:
                retry_count += 1
                logger.error(f"❌ Connection failed (attempt {retry_count}): {e}")
                
                if retry_count < max_retries:
                    wait_time = min(2 ** retry_count, 30)  # Exponential backoff, max 30s
                    logger.info(f"⏳ Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    self.send_output({
                        "type": "error",
                        "message": f"Failed to connect after {max_retries} attempts: {str(e)}",
                        "session_id": self.session_id,
                        "timestamp": datetime.now().isoformat()
                    })
                    break

    async def subscribe_to_topics(self):
        """Subscribe to relevant ROS2 topics."""
        topics_to_subscribe = []
        
        if self.include_robot_data:
            topics_to_subscribe.extend([
                ("/odom", "nav_msgs/msg/Odometry"),
                ("/battery_state", "sensor_msgs/msg/BatteryState"),
                ("/robot_pose", "geometry_msgs/msg/PoseStamped"),
                ("/cmd_vel", "geometry_msgs/msg/Twist"),
                ("/imu", "sensor_msgs/msg/Imu"),
            ])
        
        if self.include_map_data:
            topics_to_subscribe.extend([
                ("/map", "nav_msgs/msg/OccupancyGrid"),
                ("/scan", "sensor_msgs/msg/LaserScan"),
            ])
        
        for topic, msg_type in topics_to_subscribe:
            subscribe_msg = {
                "op": "subscribe",
                "topic": topic,
                "type": msg_type,
                "throttle_rate": int(1000 / self.update_frequency)  # Convert Hz to ms
            }
            await self.ros2_websocket.send(json.dumps(subscribe_msg))
            logger.info(f"📡 Subscribed to: {topic} ({msg_type})")

    async def listen_for_ros2_data(self):
        """Listen for ROS2 data and store latest values."""
        logger.info("👂 Listening for ROS2 data...")
        
        try:
            async for message in self.ros2_websocket:
                if self.should_stop:
                    break
                    
                await self.process_ros2_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ ROS2 connection closed")
            self.is_connected = False
            self.send_output({
                "type": "connection",
                "status": "disconnected",
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat(),
                "message": "ROS2 connection closed"
            })
        except Exception as e:
            logger.error(f"❌ Error listening to ROS2: {e}")
            self.send_output({
                "type": "error",
                "message": str(e),
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            })

    async def process_ros2_message(self, message):
        """Process incoming ROS2 messages."""
        try:
            data = json.loads(message)
            
            if "topic" in data and "msg" in data:
                topic = data["topic"]
                msg_data = data["msg"]
                
                # Store latest data
                if topic in self.latest_data:
                    self.latest_data[topic] = {
                        "data": msg_data,
                        "timestamp": datetime.now().isoformat(),
                        "received_at": time.time()
                    }
                    self.messages_received += 1
                    
                    # Update last data times
                    if topic in ["/odom", "/battery_state", "/robot_pose"]:
                        self.last_robot_data_time = time.time()
                    elif topic == "/map":
                        self.last_map_data_time = time.time()
                        
        except json.JSONDecodeError:
            logger.debug("Received non-JSON message from ROS2")
        except Exception as e:
            logger.error(f"Error processing ROS2 message: {e}")

    async def stream_data_periodically(self):
        """Stream data at specified frequency."""
        logger.info(f"📊 Starting periodic data streaming at {self.update_frequency} Hz")
        
        interval = 1.0 / self.update_frequency
        
        while not self.should_stop and self.is_connected:
            try:
                # Stream robot data
                if self.include_robot_data:
                    await self.stream_robot_data()
                
                # Stream map data (less frequently to avoid overwhelming)
                if self.include_map_data and self.sequence_number % 5 == 0:  # Every 5th cycle
                    await self.stream_map_data()
                
                self.sequence_number += 1
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in periodic streaming: {e}")
                await asyncio.sleep(interval)

    async def stream_robot_data(self):
        """Stream current robot data."""
        try:
            # Extract position data
            position_data = None
            if self.latest_data["/odom"] and self.latest_data["/odom"]["data"]:
                odom = self.latest_data["/odom"]["data"]
                if "pose" in odom and "pose" in odom["pose"]:
                    pose = odom["pose"]["pose"]
                    position_data = {
                        "x": pose.get("position", {}).get("x", 0),
                        "y": pose.get("position", {}).get("y", 0),
                        "z": pose.get("position", {}).get("z", 0),
                        "orientation": pose.get("orientation", {"x": 0, "y": 0, "z": 0, "w": 1}),
                        "source_topic": "/odom"
                    }
            
            # Extract battery data
            battery_data = None
            if self.latest_data["/battery_state"] and self.latest_data["/battery_state"]["data"]:
                battery = self.latest_data["/battery_state"]["data"]
                battery_data = {
                    "percentage": battery.get("percentage", None),
                    "voltage": battery.get("voltage", None),
                    "current": battery.get("current", None),
                    "source_topic": "/battery_state"
                }
            
            # Send robot data if we have any
            if position_data or battery_data:
                self.send_output({
                    "type": "robot_data",
                    "session_id": self.session_id,
                    "position": position_data,
                    "battery_level": battery_data,
                    "timestamp": datetime.now().isoformat(),
                    "sequence": self.sequence_number
                })
                
        except Exception as e:
            logger.error(f"Error streaming robot data: {e}")

    async def stream_map_data(self):
        """Stream current map data."""
        try:
            if not self.latest_data["/map"] or not self.latest_data["/map"]["data"]:
                return
            
            map_msg = self.latest_data["/map"]["data"]
            
            # Extract map info
            info = map_msg.get("info", {})
            map_info = {
                "width": info.get("width", 0),
                "height": info.get("height", 0),
                "resolution": info.get("resolution", 0.05),
                "origin": {
                    "position": info.get("origin", {}).get("position", {"x": 0, "y": 0, "z": 0}),
                    "orientation": info.get("origin", {}).get("orientation", {"x": 0, "y": 0, "z": 0, "w": 1})
                },
                "map_load_time": info.get("map_load_time", {}).get("sec", 0)
            }
            
            # Get map data
            map_data = map_msg.get("data", [])
            
            # Calculate statistics
            total_cells = len(map_data)
            free_cells = sum(1 for cell in map_data if cell == 0)
            occupied_cells = sum(1 for cell in map_data if cell == 100)
            unknown_cells = sum(1 for cell in map_data if cell == -1)
            
            statistics = {
                "total_cells": total_cells,
                "free_cells": free_cells,
                "occupied_cells": occupied_cells,
                "unknown_cells": unknown_cells,
                "free_percentage": (free_cells / total_cells * 100) if total_cells > 0 else 0,
                "occupied_percentage": (occupied_cells / total_cells * 100) if total_cells > 0 else 0,
                "unknown_percentage": (unknown_cells / total_cells * 100) if total_cells > 0 else 0,
            }
            
            # Send map data
            self.send_output({
                "type": "map_data",
                "session_id": self.session_id,
                "info": map_info,
                "data": map_data,
                "statistics": statistics,
                "is_full_update": True,
                "timestamp": datetime.now().isoformat(),
                "sequence": self.sequence_number,
                "data_length": len(map_data)
            })
            
        except Exception as e:
            logger.error(f"Error streaming map data: {e}")

    def send_output(self, data: Dict[str, Any]):
        """Send JSON output to stdout for NestJS service to read."""
        try:
            print(json.dumps(data), flush=True)
        except Exception as e:
            logger.error(f"Error sending output: {e}")

    async def cleanup(self):
        """Clean up resources."""
        logger.info("🧹 Cleaning up...")
        
        self.should_stop = True
        
        if self.ros2_websocket and not self.ros2_websocket.closed:
            await self.ros2_websocket.close()
        
        self.send_output({
            "type": "connection",
            "status": "disconnected",
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "Bridge shutdown"
        })
        
        logger.info("✅ Cleanup completed")

async def main():
    parser = argparse.ArgumentParser(description='Enhanced Real-time Robot Data Bridge')
    parser.add_argument('--ip', required=True, help='ROS2 bridge IP address')
    parser.add_argument('--port', type=int, default=8765, help='ROS2 bridge port')
    parser.add_argument('--session-id', help='Stream session ID')
    parser.add_argument('--update-frequency', type=float, default=2.0, help='Update frequency in Hz')
    parser.add_argument('--include-robot-data', action='store_true', help='Include robot position and battery data')
    parser.add_argument('--include-map-data', action='store_true', help='Include map data')
    
    args = parser.parse_args()
    
    bridge = RealtimeRobotBridge(
        ros2_ip=args.ip,
        ros2_port=args.port,
        session_id=args.session_id,
        update_frequency=args.update_frequency,
        include_robot_data=args.include_robot_data,
        include_map_data=args.include_map_data
    )
    
    try:
        await bridge.start_streaming()
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
    finally:
        await bridge.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
