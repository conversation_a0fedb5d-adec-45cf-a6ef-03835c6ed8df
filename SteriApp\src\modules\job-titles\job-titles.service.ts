import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { DepartmentsService } from "../departments/departments.service"
import type { CreateJobTitleDto } from "./dto/create-job-title.dto"
import type { UpdateJobTitleDto } from "./dto/update-job-title.dto"

@Injectable()
export class JobTitlesService {
  private readonly collection = "jobTitles"

  constructor(
    private firebaseService: FirebaseService,
    private departmentsService: DepartmentsService
  ) {}

  async create(createJobTitleDto: CreateJobTitleDto) {
    const firestore = this.firebaseService.getFirestore()

    // Validate department exists
    await this.departmentsService.findById(createJobTitleDto.departmentId)

    // Check if job title name already exists in the same department
    const existingJobTitle = await this.findByNameAndDepartment(
      createJobTitleDto.name, 
      createJobTitleDto.departmentId
    )
    if (existingJobTitle) {
      throw new BadRequestException(
        `Job title '${createJobTitleDto.name}' already exists in this department`
      )
    }

    const jobTitleData = {
      ...createJobTitleDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const docRef = await firestore.collection(this.collection).add(jobTitleData)
    return { id: docRef.id, ...jobTitleData }
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).orderBy('name').get()
    const jobTitles = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))

    // Populate department information
    const populatedJobTitles = await Promise.all(
      jobTitles.map(async (jobTitle: any) => {
        try {
          const department: any = await this.departmentsService.findById(jobTitle.departmentId)
          return {
            ...jobTitle,
            department: {
              id: department.id,
              name: department.name,
              description: department.description
            }
          }
        } catch (error) {
          // If department not found, return job title without department info
          return jobTitle
        }
      })
    )

    return populatedJobTitles
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Job title with ID ${id} not found`)
    }

    const jobTitle = { id: doc.id, ...doc.data() } as any

    // Populate department information
    try {
      const department: any = await this.departmentsService.findById(jobTitle.departmentId)
      jobTitle.department = {
        id: department.id,
        name: department.name,
        description: department.description
      }
    } catch (error) {
      // If department not found, continue without department info
    }

    return jobTitle
  }

  async findByNameAndDepartment(name: string, departmentId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection)
      .where('name', '==', name)
      .where('departmentId', '==', departmentId)
      .get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() }
  }

  async findByDepartment(departmentId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection)
      .where('departmentId', '==', departmentId)
      .orderBy('name')
      .get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async update(id: string, updateJobTitleDto: UpdateJobTitleDto) {
    try {
      const firestore = this.firebaseService.getFirestore()
      const docRef = firestore.collection(this.collection).doc(id)

      // Check if document exists first
      const doc = await docRef.get()
      if (!doc.exists) {
        throw new NotFoundException(`Job title with ID ${id} not found`)
      }

      // Validate department exists if provided
      if (updateJobTitleDto.departmentId) {
        await this.departmentsService.findById(updateJobTitleDto.departmentId)
      }

      // Check if new name already exists in the department (if name or department is being updated)
      if (updateJobTitleDto.name || updateJobTitleDto.departmentId) {
        const currentData = doc.data()
        const nameToCheck = updateJobTitleDto.name || currentData?.name
        const deptToCheck = updateJobTitleDto.departmentId || currentData?.departmentId

        const existingJobTitle = await this.findByNameAndDepartment(nameToCheck, deptToCheck)
        if (existingJobTitle && existingJobTitle.id !== id) {
          throw new BadRequestException(
            `Job title '${nameToCheck}' already exists in this department`
          )
        }
      }

      const updateData = {
        ...updateJobTitleDto,
        updatedAt: new Date(),
      }

      await docRef.update(updateData)
      return this.findById(id)
    } catch (error) {
      console.error('Error updating job title:', error)
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new Error(`Failed to update job title: ${error.message}`)
    }
  }

  async remove(id: string) {
    try {
      const firestore = this.firebaseService.getFirestore()

      // Check if job title exists
      const doc = await firestore.collection(this.collection).doc(id).get()
      if (!doc.exists) {
        throw new NotFoundException(`Job title with ID ${id} not found`)
      }

      // TODO: Check if job title is assigned to any users
      // This will be implemented when we update the users service

      await firestore.collection(this.collection).doc(id).delete()
      return { message: "Job title deleted successfully" }
    } catch (error) {
      console.error('Error deleting job title:', error)
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new Error(`Failed to delete job title: ${error.message}`)
    }
  }
}
