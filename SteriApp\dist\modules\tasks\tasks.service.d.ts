import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateTaskDto } from "./dto/create-task.dto";
import type { UpdateTaskDto } from "./dto/update-task.dto";
export declare class TasksService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createTaskDto: CreateTaskDto): Promise<{
        taskId: string;
        status: string;
        createdAt: Date;
        taskName: string;
        taskType: import("./dto/create-task.dto").TaskType;
        priority: import("./dto/create-task.dto").TaskPriority;
        scheduledTime?: string;
        createdBy: string;
        robotId: string;
        zoneId: string;
        estimatedDuration?: number;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    findByUser(userId: string): Promise<{
        id: string;
    }[]>;
    findByRobot(robotId: string): Promise<{
        id: string;
    }[]>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: string): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
