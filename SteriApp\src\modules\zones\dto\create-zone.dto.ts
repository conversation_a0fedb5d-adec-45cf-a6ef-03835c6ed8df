import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsBoolean, IsDate<PERSON>tring, <PERSON><PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateZoneDto {
  @ApiProperty()
  @IsString()
  zoneName: string

  @ApiProperty()
  @IsString()
  roomId: string

  @ApiProperty()
  @IsString()
  coordinates: string

  @ApiProperty()
  @IsString()
  zoneType: string

  @ApiProperty()
  @IsString()
  priority: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  disinfectionRequired?: boolean

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accessPoints?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  lastDisinfected?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  disinfectionFrequency?: number
}
