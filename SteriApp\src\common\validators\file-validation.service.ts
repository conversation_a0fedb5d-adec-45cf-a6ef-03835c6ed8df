import { Injectable, BadRequestException } from '@nestjs/common'

@Injectable()
export class FileValidationService {
  private readonly allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ]

  private readonly maxFileSize = 5 * 1024 * 1024 // 5MB in bytes

  /**
   * Validate uploaded image file
   * @param file - Uploaded file
   * @throws BadRequestException if validation fails
   */
  validateImageFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BadRequestException('No file uploaded')
    }

    // Check file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size too large. Maximum allowed size is ${this.maxFileSize / (1024 * 1024)}MB`
      )
    }

    // Check MIME type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`
      )
    }

    // Additional validation for file extension
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp']
    const fileExtension = this.getFileExtension(file.originalname).toLowerCase()
    
    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestException(
        `Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`
      )
    }
  }

  /**
   * Generate unique filename for uploaded image
   * @param userId - User ID
   * @param originalName - Original filename
   * @returns string - Unique filename
   */
  generateUniqueFileName(userId: string, originalName: string): string {
    const timestamp = Date.now()
    const extension = this.getFileExtension(originalName)
    return `${userId}_${timestamp}${extension}`
  }

  /**
   * Get file extension from filename
   * @param filename - Original filename
   * @returns string - File extension with dot
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : ''
  }

  /**
   * Get content type from file extension
   * @param filename - Filename with extension
   * @returns string - MIME type
   */
  getContentTypeFromExtension(filename: string): string {
    const extension = this.getFileExtension(filename).toLowerCase()
    
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg'
      case '.png':
        return 'image/png'
      case '.webp':
        return 'image/webp'
      default:
        return 'application/octet-stream'
    }
  }
}
