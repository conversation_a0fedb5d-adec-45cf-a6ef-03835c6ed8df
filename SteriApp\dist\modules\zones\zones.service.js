"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZonesService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let ZonesService = class ZonesService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "zones";
    }
    async create(createZoneDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const zoneData = {
            ...createZoneDto,
            zoneId: docRef.id,
            createdAt: new Date(),
        };
        await docRef.set(zoneData);
        return zoneData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findByRoom(roomId) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where("roomId", "==", roomId).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Zone with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateZoneDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...updateZoneDto,
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async markAsDisinfected(id) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            lastDisinfected: new Date(),
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "Zone deleted successfully" };
    }
};
exports.ZonesService = ZonesService;
exports.ZonesService = ZonesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], ZonesService);
//# sourceMappingURL=zones.service.js.map